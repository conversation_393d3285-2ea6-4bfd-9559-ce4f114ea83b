// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// @calmMonkey
// Mik3Christ3ns3n's M2 Global Liquidity Index Moved forward 10 weeks.
// M2 Money Stock for CNY+USD+EUR+JPY+GBP

//@version=6
indicator('M2 Global Liquidity Index - 10 Week Lead', overlay=true, scale=scale.left)

// Define offset in weeks
offset_weeks = 10

// Convert weeks to milliseconds (1 week = 7 days * 86400000 ms/day)
offset_ms = offset_weeks * 7 * 86400000

cnm2   = request.security("ECONOMICS:CNM2", "D", close)
cnyusd = request.security("FX_IDC:CNYUSD", "D", close)

usm2 = request.security("ECONOMICS:USM2", "D", close)

eum2 = request.security("ECONOMICS:EUM2", "D", close)
eurusd = request.security("FX:EURUSD", "D", close)

jpm2 = request.security("ECONOMICS:JPM2", "D", close)
jpyusd = request.security("FX_IDC:JPYUSD", "D", close)

gbm2 = request.security("ECONOMICS:GBM2", "D", close)
gbpusd = request.security("FX:GBPUSD", "D", close)

total = (cnm2 * cnyusd + usm2 + eum2 * eurusd + jpm2 * jpyusd + gbm2 * gbpusd) / 1000000000000

// Plot with the time offset
plot(total, color=color.rgb(85, 85, 79), linewidth=2, offset=offset_weeks * 7, title="M2 Global Liquidity")