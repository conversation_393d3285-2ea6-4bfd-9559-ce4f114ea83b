// Auto Fibonacci Retracement Kagebunshin + Swing Level Labels
//@version=5
indicator("Kagebunshin With Auto Fibonacci Retracement + Swing Level Labels", "Kagebunshin", overlay=true)
g_afr = 'Auto Fibonacci Retracement'
// g_ms = 'Market Structure'
g_sl = 'Swing level labels'
// g_slm = 'SALMA'
// g_sr = 'Auto Support and Ressistance'
show_fib = input(true, "Show Fibonacci Retracement", display = display.data_window)
devTooltip = "Deviation is a multiplier that affects how much the price should deviate from the previous pivot in order for the bar to become a new pivot."
depthTooltip = "The minimum number of bars that will be taken into account when calculating the indicator."
// pivots threshold
threshold_multiplier = input.float(title="Deviation", defval=3, minval=0, tooltip=devTooltip, group = g_afr)
depth = input.int(title="Depth", defval=10, minval=2, tooltip=depthTooltip, group = g_afr)
reverse = input(false, "Reverse", display = display.data_window, group = g_afr)
var extendLeft = input(false, "Extend Left    |    Extend Right", inline = "Extend Lines", group = g_afr)
var extendRight = input(true, "", inline = "Extend Lines", group = g_afr)
var extending = extend.none
if extendLeft and extendRight
    extending := extend.both
if extendLeft and not extendRight
    extending := extend.left
if not extendLeft and extendRight
    extending := extend.right
prices = input(true, "Show Prices", display = display.data_window)
levels = input(true, "Show Levels", inline = "Levels", display = display.data_window)
levelsFormat = input.string("Values", "", options = ["Values", "Percent"], inline = "Levels", display = display.data_window)
labelsPosition = input.string("Right", "Labels Position", options = ["Left", "Right"], display = display.data_window)
var int backgroundTransparency = input.int(100, "Background Transparency", minval = 0, maxval = 100, display = display.data_window)

import TradingView/ZigZag/7 as zigzag

update()=>
    var settings = zigzag.Settings.new(threshold_multiplier, depth, color(na), false, false, false, false, "Absolute", true)
    var zigzag.ZigZag zigZag = zigzag.newInstance(settings)
    var zigzag.Pivot lastP = na
    var float startPrice = na
    var float height = na
    if barstate.islast and zigZag.pivots.size() < 2
        runtime.error("Not enough data to calculate Auto Fib Retracement on the current symbol. Change the chart's timeframe to a lower one or select a smaller calculation depth using the indicator's `Depth` settings.")
    settings.devThreshold := ta.atr(10) / close * 100 * threshold_multiplier
    if zigZag.update()
        lastP := zigZag.lastPivot()
        if not na(lastP)
            var line lineLast = na
            if na(lineLast)
                lineLast := line.new(lastP.start, lastP.end, xloc=xloc.bar_time, color=color.gray, width=1, style=line.style_dashed)
            else
                line.set_first_point(lineLast, lastP.start)
                line.set_second_point(lineLast, lastP.end)

            startPrice := reverse ? lastP.start.price : lastP.end.price
            endPrice = reverse ? lastP.end.price : lastP.start.price
            height := (startPrice > endPrice ? -1 : 1) * math.abs(startPrice - endPrice)
    [lastP, startPrice, height]

[lastP, startPrice, height] = update()

_draw_line(price, col) =>
    // Define how many bars to extend to the right (10 bars ahead)
    var int bar_extend = 10
    // Calculate the time of the bar that is 10 bars ahead
    var future_time = time[bar_extend]

    // If there are enough bars, use future_time, otherwise fall back to current time
    if not na(future_time)
        future_time := time + (time - time[bar_extend])

    // Create a new line with the future_time
    var id = line.new(lastP.start.time, lastP.start.price, future_time, price, xloc=xloc.bar_time, color=col, width=1, style=line.style_dashed)

    line.set_xy1(id, lastP.start.time, price)
    line.set_xy2(id, future_time, price)
    id



_draw_label(price, txt, txtColor) =>
    x = labelsPosition == "Left" ? lastP.start.time : not extendRight ? lastP.end.time : time
    labelStyle = labelsPosition == "Left" ? label.style_label_right : label.style_label_left
    align = labelsPosition == "Left" ? text.align_right : text.align_left
    labelsAlignStrLeft = txt + '\n ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏ \n'
    labelsAlignStrRight = '       ' + txt + '\n ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏ \n'
    labelsAlignStr = labelsPosition == "Left" ? labelsAlignStrLeft : labelsAlignStrRight
    var id = label.new(x=x, y=price, xloc=xloc.bar_time, text=labelsAlignStr, textcolor=txtColor, style=labelStyle, textalign=align, color=#00000000)
    label.set_xy(id, x, price)
    label.set_text(id, labelsAlignStr)
    label.set_textcolor(id, txtColor)

_wrap(txt) =>
    "(" + str.tostring(txt, format.mintick) + ")"

_label_txt(level, price) =>
    l = levelsFormat == "Values" ? str.tostring(level) : str.tostring(level * 100) + "%"
    (levels ? l : "") + (prices ? _wrap(price) : "")

_crossing_level(series float sr, series float r) =>
    (r > sr and r < sr[1]) or (r < sr and r > sr[1])

processLevel(bool show, float value, color colorL, line lineIdOther) =>
    if show and show_fib and not na(lastP)
        float m = value
        r = startPrice + height * m
        crossed = _crossing_level(close, r)
        lineId = _draw_line(r, colorL)

        // Check for specific Fibonacci levels and assign the corresponding "Kagebunshin" text
        var string kagebunshinTxt = ""
        if (m == 0.5)
            kagebunshinTxt := "Kagebunshin 1"
        else if (m == 0.618)
            kagebunshinTxt := "Kagebunshin 2"
        else if (m == 0.786)
            kagebunshinTxt := "Kagebunshin 3"
        else if (m == 1)
            kagebunshinTxt := "Kagebunshin 4"
        else if (m == 0.382)
            kagebunshinTxt := "Kagebunshin 2"
        else if (m == 0.236)
            kagebunshinTxt := "Kagebunshin 3"
        else if (m == 0)
            kagebunshinTxt := "Kagebunshin 4"

        // Append the "Kagebunshin" label to the Fibonacci level only if it matches
        if (kagebunshinTxt != "")
            _draw_label(r, _label_txt(m, r) + " " + kagebunshinTxt, colorL)
        else
            _draw_label(r, _label_txt(m, r), colorL)

        if crossed
            alert("Autofib: " + syminfo.ticker + " crossing level " + str.tostring(value))
        if not na(lineIdOther)
            linefill.new(lineId, lineIdOther, color = color.new(colorL, backgroundTransparency))
        lineId
    else
        lineIdOther


show_0 = input(true, "", inline = "Level0", display = display.data_window)
value_0 = input(0, "", inline = "Level0", display = display.data_window)
color_0 = input(#787b86, "", inline = "Level0", display = display.data_window)

show_0_236 = input(true, "", inline = "Level0", display = display.data_window)
value_0_236 = input(0.236, "", inline = "Level0", display = display.data_window)
color_0_236 = input(#81c784, "", inline = "Level0", display = display.data_window)

show_0_382 = input(true, "", inline = "Level1", display = display.data_window)
value_0_382 = input(0.382, "", inline = "Level1", display = display.data_window)
color_0_382 = input(#81c784, "", inline = "Level1", display = display.data_window)

show_0_5 = input(true, "", inline = "Level1", display = display.data_window)
value_0_5 = input(0.5, "", inline = "Level1", display = display.data_window)
color_0_5 = input(#009688, "", inline = "Level1", display = display.data_window)

show_0_618 = input(true, "", inline = "Level2", display = display.data_window)
value_0_618 = input(0.618, "", inline = "Level2", display = display.data_window)
color_0_618 = input(#f44336, "", inline = "Level2", display = display.data_window)

show_0_65 = input(false, "", inline = "Level2", display = display.data_window)
value_0_65 = input(0.65, "", inline = "Level2", display = display.data_window)
color_0_65 = input(#f44336, "", inline = "Level2", display = display.data_window)

show_0_786 = input(true, "", inline = "Level3", display = display.data_window)
value_0_786 = input(0.786, "", inline = "Level3", display = display.data_window)
color_0_786 = input(#f44336, "", inline = "Level3", display = display.data_window)

show_1 = input(true, "", inline = "Level3", display = display.data_window)
value_1 = input(1, "", inline = "Level3", display = display.data_window)
color_1 = input(#787b86, "", inline = "Level3", display = display.data_window)

show_1_272 = input(false, "", inline = "Level4", display = display.data_window)
value_1_272 = input(1.272, "", inline = "Level4", display = display.data_window)
color_1_272 = input(#f44336, "", inline = "Level4", display = display.data_window)

show_1_414 = input(false, "", inline = "Level4", display = display.data_window)
value_1_414 = input(1.414, "", inline = "Level4", display = display.data_window)
color_1_414 = input(#f44336, "", inline = "Level4", display = display.data_window)

show_1_618 = input(false, "", inline = "Level5", display = display.data_window)
value_1_618 = input(1.618, "", inline = "Level5", display = display.data_window)
color_1_618 = input(#f44336, "", inline = "Level5", display = display.data_window)

show_1_65 = input(false, "", inline = "Level5", display = display.data_window)
value_1_65 = input(1.65, "", inline = "Level5", display = display.data_window)
color_1_65 = input(#f44336, "", inline = "Level5", display = display.data_window)

show_2_618 = input(false, "", inline = "Level6", display = display.data_window)
value_2_618 = input(2.618, "", inline = "Level6", display = display.data_window)
color_2_618 = input(#f44336, "", inline = "Level6", display = display.data_window)

show_2_65 = input(false, "", inline = "Level6", display = display.data_window)
value_2_65 = input(2.65, "", inline = "Level6", display = display.data_window)
color_2_65 = input(#f44336, "", inline = "Level6", display = display.data_window)

show_3_618 = input(false, "", inline = "Level7", display = display.data_window)
value_3_618 = input(3.618, "", inline = "Level7", display = display.data_window)
color_3_618 = input(#f44336, "", inline = "Level7", display = display.data_window)

show_3_65 = input(false, "", inline = "Level7", display = display.data_window)
value_3_65 = input(3.65, "", inline = "Level7", display = display.data_window)
color_3_65 = input(#f44336, "", inline = "Level7", display = display.data_window)

show_4_236 = input(false, "", inline = "Level8", display = display.data_window)
value_4_236 = input(4.236, "", inline = "Level8", display = display.data_window)
color_4_236 = input(#e91e63, "", inline = "Level8", display = display.data_window)

show_4_618 = input(false, "", inline = "Level8", display = display.data_window)
value_4_618 = input(4.618, "", inline = "Level8", display = display.data_window)
color_4_618 = input(#f44336, "", inline = "Level8", display = display.data_window)

show_neg_0_236 = input(false, "", inline = "Level9", display = display.data_window)
value_neg_0_236 = input(-0.236, "", inline = "Level9", display = display.data_window)
color_neg_0_236 = input(#81c784, "", inline = "Level9", display = display.data_window)

show_neg_0_382 = input(false, "", inline = "Level9", display = display.data_window)
value_neg_0_382 = input(-0.382, "", inline = "Level9", display = display.data_window)
color_neg_0_382 = input(#81c784, "", inline = "Level9", display = display.data_window)

show_neg_0_618 = input(false, "", inline = "Level10", display = display.data_window)
value_neg_0_618 = input(-0.618, "", inline = "Level10", display = display.data_window)
color_neg_0_618 = input(#81c784, "", inline = "Level10", display = display.data_window)

show_neg_0_65 = input(false, "", inline = "Level10", display = display.data_window)
value_neg_0_65 = input(-0.65, "", inline = "Level10", display = display.data_window)
color_neg_0_65 = input(#81c784, "", inline = "Level10", display = display.data_window)


if show_fib
    lineId0 = processLevel(show_neg_0_65, value_neg_0_65, color_neg_0_65, line(na))
    lineId1 = processLevel(show_neg_0_618, value_neg_0_618, color_neg_0_618, lineId0)
    lineId2 = processLevel(show_neg_0_382, value_neg_0_382, color_neg_0_382, lineId1)
    lineId3 = processLevel(show_neg_0_236, value_neg_0_236, color_neg_0_236, lineId2)
    lineId4 = processLevel(show_0, value_0, color_0, lineId3)
    lineId5 = processLevel(show_0_236, value_0_236, color_0_236, lineId4)
    lineId6 = processLevel(show_0_382, value_0_382, color_0_382, lineId5)
    lineId7 = processLevel(show_0_5, value_0_5, color_0_5, lineId6)
    lineId8 = processLevel(show_0_618, value_0_618, color_0_618, lineId7)
    lineId9 = processLevel(show_0_65, value_0_65, color_0_65, lineId8)
    lineId10 = processLevel(show_0_786, value_0_786, color_0_786, lineId9)
    lineId11 = processLevel(show_1, value_1, color_1, lineId10)
    lineId12 = processLevel(show_1_272, value_1_272, color_1_272, lineId11)
    lineId13 = processLevel(show_1_414, value_1_414, color_1_414, lineId12)
    lineId14 = processLevel(show_1_618, value_1_618, color_1_618, lineId13)
    lineId15 = processLevel(show_1_65, value_1_65, color_1_65, lineId14)
    lineId16 = processLevel(show_2_618, value_2_618, color_2_618, lineId15)
    lineId17 = processLevel(show_2_65, value_2_65, color_2_65, lineId16)
    lineId18 = processLevel(show_3_618, value_3_618, color_3_618, lineId17)
    lineId19 = processLevel(show_3_65, value_3_65, color_3_65, lineId18)
    lineId20 = processLevel(show_4_236, value_4_236, color_4_236, lineId19)
    lineId21 = processLevel(show_4_618, value_4_618, color_4_618, lineId20)

//Swing level labels
showSwing = input.bool(true, 'Show Swing Points', tooltip='Show or hide HH, LH, HL, LL', group = g_sl)
// Constants
color CLEAR = color.rgb(0,0,0,100)

// Inputs
swingSize = input.int(20, 'Swing Length', tooltip='The number of left and right bars checked when searching for a swing point. Higher value = less swing points plotted and lower value = more swing points plotted.')

// Calculations

// Finding high and low pivots
pivHi = ta.pivothigh(high, swingSize, swingSize)
pivLo = ta.pivotlow(low, swingSize, swingSize)

// Tracking the previous swing levels to determine HH, LH, HL, LL
var float prevHigh = na
var float prevLow = na

bool hh = false
bool lh = false
bool hl = false
bool ll = false

if not na(pivHi)
    if pivHi >= prevHigh
        hh := true
    else
        lh := true
    prevHigh := pivHi

if not na(pivLo)
    if pivLo >= prevLow
        hl := true
    else
        ll := true
    prevLow := pivLo

// Visual Output

// Swing level labels, only display if showSwing is enabled
if showSwing
    if hh
        label.new(bar_index - swingSize, pivHi, 'HH', color=CLEAR, style=label.style_label_down, textcolor=chart.fg_color)

    if lh
        label.new(bar_index - swingSize, pivHi, 'LH', color=CLEAR, style=label.style_label_down, textcolor=chart.fg_color)

    if hl
        label.new(bar_index - swingSize, pivLo, 'HL', color=CLEAR, style=label.style_label_up, textcolor=chart.fg_color)

    if ll
        label.new(bar_index - swingSize, pivLo, 'LL', color=CLEAR, style=label.style_label_up, textcolor=chart.fg_color)