// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © ChartPrime

//@version=5
indicator("Multi Deviation Scaled Moving Average + Smooth And Lazy Moving Average + Supertrended + Market Structure", "Multi DSMA + SALMA + ST + MS", overlay=true)
g_dsma = 'Multi Deviation Scaled Moving Average'
g_ob = 'Volumized Order Blocks'
g_slm = 'SALMA'
g_sst = 'Show Supertrend'
g_st = 'Supertrended'
g_ms = 'Market Structure'
// ---------------------------------------------------------------------------------------------------------------------}
// 𝙐𝙎𝙀𝙍 𝙄𝙉𝙋𝙐𝙏𝙎
// ---------------------------------------------------------------------------------------------------------------------{

int period          = input.int(30, title="Period", group = g_dsma)
int step            = 100 - input.int(60, "Sensitivity", minval = 0, maxval = 100,  tooltip = "The Lower input Lower sensitivity", group = g_dsma)
series float src    = hlc3

// Colors for visualization with user input
color upper_color   = input.color(color.rgb(104, 230, 20), title="Upper (Bullish) Color", group = g_dsma)
color down_color    = input.color(color.rgb(200, 45, 45), title="Lower (Bearish) Color", group = g_dsma)

// Show or Hide options
bool show_ma        = input.bool(true, title="Show MA Line", group = g_dsma)
bool show_signals   = input.bool(true, title="Show Signals", group = g_dsma)
bool show_histogram = input.bool(true, title="Show Histogram", group = g_dsma)

// ---------------------------------------------------------------------------------------------------------------------}
// 𝙄𝙉𝘿𝙄𝘾𝘼𝙏𝙊𝙍 𝘾𝘼𝙇𝘾𝙐𝙇𝘼𝙏𝙄𝙊𝙉𝙎
// ---------------------------------------------------------------------------------------------------------------------{

// @function Calculates the Deviation Scaled Moving Average (DSMA)
// @param src (series float) Source price series
// @param period (int) Calculation period
// @returns (float) DSMA value
dsma(src, int period)=>
    // Variables initialization
    var float a1   = 0.0
    var float b1   = 0.0
    var float c1   = 0.0
    var float c2   = 0.0
    var float c3   = 0.0
    var float filt = 0.0
    var float dsma = 0.0
    var float s    = 0.0

    // Initialize variables on the first bar
    if barstate.isfirst
        pi = 3.1415926535897932
        g  = math.sqrt(2)
        // Smooth with a Super Smoother
        s  := 2 * pi / period
        a1 := math.exp(-g * pi / (0.5 * period))
        b1 := 2 * a1 * math.cos(g * s / (0.5 * period))
        c2 := b1
        c3 := -a1 * a1
        c1 := 1 - c2 - c3

    // Produce Nominal zero mean with zeros in the transfer response at DC and Nyquist with no spectral distortion
    zeros = (close - close[2])
    // SuperSmoother Filter
    filt := c1 * (zeros + zeros[1]) / 2 + c2 * nz(filt[1]) + c3 * nz(filt[2])

    // Compute Standard Deviation
    rms = math.sqrt(ta.ema(math.pow(filt, 2), period))
    // Rescale Filt in terms of Standard Deviations
    scaled_filt = rms != 0 ? filt / rms : 0
    alpha1 = math.abs(scaled_filt) * 5 / period
    dsma := alpha1 * close + (1 - alpha1) * nz(dsma[1])

    dsma

// @function Calculates trend percentage, color, and average DSMA
// @param src (series float) Source price series
// @param period (int) Initial calculation period
// @param step (int) Step size for increasing period
// @returns [float, float, color] Score, average DSMA, and color
percent_trend(src, period, step)=>
    dsma_arr = array.new<float>()

    // Calculate DSMAs with increasing periods
    length  = period
    dsma1   = dsma(src, length)
    length += step
    dsma2   = dsma(src, length)
    length += step
    dsma3   = dsma(src, length)
    length += step
    dsma4   = dsma(src, length)
    length += step
    dsma5   = dsma(src, length)
    length += step
    dsma6   = dsma(src, length)
    length += step
    dsma7   = dsma(src, length)
    length += step
    dsma8   = dsma(src, length)

    // Store DSMAs in array
    dsma_arr.push(dsma1)
    dsma_arr.push(dsma2)
    dsma_arr.push(dsma3)
    dsma_arr.push(dsma4)
    dsma_arr.push(dsma5)
    dsma_arr.push(dsma6)
    dsma_arr.push(dsma7)
    dsma_arr.push(dsma8)

    val = 0.14285714285714285714285714285714

    // Calculate score based on DSMA comparisons
    score = 0.
    for i = 0 to dsma_arr.size() - 1
        dsma = dsma_arr.get(i)
        if dsma > dsma_arr.get(7)
            score += val

    // Determine color based on score
    color =  score > 0.5
             ? color.from_gradient(score, 0.5, 1, na, upper_color)
             : color.from_gradient(score, 0, 0.5, down_color, na)

    [score, dsma_arr.avg(), color]

// Calculate trend percentage, average DSMA, and color
[score, ma, color] = percent_trend(src, period, step)

up_percent = score * 100
dn_percent = 100 - up_percent

// ---------------------------------------------------------------------------------------------------------------------}
// 𝙑𝙄𝙎𝙐𝘼𝙇𝙄𝙕𝘼𝙏𝙄𝙊𝙉
// ---------------------------------------------------------------------------------------------------------------------{

// Creates a trend histogram visualization (only if show_histogram is enabled)
if show_histogram and barstate.islast
    tbl = table.new(position.bottom_right, 100, 100)

    // Draw upper (bullish) part of histogram
    for i = 1 to math.round(up_percent/2)
        color_up = color.from_gradient(i, 0, 50, color.new(upper_color, 80), color.new(upper_color, 0))
        tbl.cell(0, 50-i, "", 1, 1, bgcolor = color_up)

        if i == math.round(up_percent/2)
            tbl.cell(0, 50-i, str.tostring(math.round(up_percent)), 1, 2, text_color = color_up, text_size = size.small)

    // Draw lower (bearish) part of histogram
    for i = 1 to math.round(dn_percent/2)
        color_dn = color.from_gradient(i, 0, 50, color.new(down_color, 80), color.new(down_color, 0))
        tbl.cell(1, 50-i, "", 1, 1, bgcolor = color_dn)

        if i == math.round(dn_percent/2)
            tbl.cell(1, 50-i, str.tostring(math.round(dn_percent)), 1, 2, text_color = color_dn, text_size = size.small)

// Detect crossovers for signal generation
cross_up = ta.crossover(score, 0.3)
cross_dn = ta.crossunder(score, 0.7)

// Control the visibility of signals using conditional logic
plotshape(show_signals and cross_up ? ma : na, "", shape.diamond, location.absolute, color.new(upper_color, 50), size = size.small, force_overlay = true)
plotshape(show_signals and cross_up ? ma : na, "", shape.diamond, location.absolute, upper_color, size = size.tiny, force_overlay = true)

plotshape(show_signals and cross_dn ? ma : na, "", shape.diamond, location.absolute, color.new(down_color, 50), size = size.small, force_overlay = true)
plotshape(show_signals and cross_dn ? ma : na, "", shape.diamond, location.absolute, down_color, size = size.tiny, force_overlay = true)

// Plot the main indicator line (only if show_ma is enabled)
plot(show_ma ? ma : na, color = color, linewidth = 2)

// ---------------------------------------------------------------------------------------------------------------------}

// SALMA

// Corrects price points within specific StdDev band before calculating a smoothed WMA

price       = input(close, 'Source', group = g_slm)
length      = input.int(10, 'Length', minval=1, group = g_slm)
smooth      = input.int(3, 'Extra Smooth [1 = None]', minval=1, group = g_slm)

mult        = input.float(0.3, minval=0.05, maxval=3, step=0.05,
  title='Width', inline = 'SD Channel', group='Volatility Filter (SD Channel)')

sd_len      = input.int(5, minval=1,
  title='Length', inline = 'SD Channel', group='Volatility Filter (SD Channel)')

baseline    = ta.wma(price, sd_len)
dev         = mult * ta.stdev(price, sd_len)
upper       = baseline + dev
lower       = baseline - dev

cprice      = price > upper ? upper : price < lower ? lower : price

// Uncomment these code lines to expose the base StdDev channel used as volatility filter
//plot (baseline, "Base MA")
//plot(upper, "Upper Band", color=color.green)
//plot(lower, "Lower Band", color=color.red)

REMA        = ta.wma(ta.wma(cprice, length), smooth)

c_up        = color.new(#33ff00, 0)
c_dn        = color.new(#ff1111, 0)

REMA_up     = REMA > REMA[1]

// Add show or hide option for the SALMA line
show_salma  = input.bool(true, title="Show SALMA Line", group="SALMA Settings")
plot(show_salma ? REMA : na, title='SALMA', color=REMA_up ? c_up : c_dn, linewidth=3)

// ======================================================================================================
// Add optional MA's - to enable us to track what many other traders are working with
// These MA's will be hidden by default until user exposes them as needed in the Settings
// The below code is based on the built-in MA Ribbon in the TV library - with some modifications

// ======================================================================
f_ma(source, length, mtype) =>
    mtype    == 'SMA' ? ta.sma(source, length) :
      mtype  == 'EMA' ? ta.ema(source, length) :
      ta.wma(source, length)
// ======================================================================
gr_ma       = 'Optional MA\'s'
t_ma1       = 'MA #1'
t_ma2       = 'MA #2'
t_ma3       = 'MA #3'

// MA #1
show_ma1    = input.bool(false, t_ma1, inline=t_ma1, group=gr_ma)
ma1_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma1, group=gr_ma)
ma1_source  = input.source(close, '', inline=t_ma1, group=gr_ma)
ma1_length  = input.int(50, '', minval=1, inline=t_ma1, group=gr_ma)
ma1_color   = #59ff00
ma1         = f_ma(ma1_source, ma1_length, ma1_type)
plot(show_ma1 ? ma1 : na, color = color.new(ma1_color, 0), title = t_ma1, linewidth = 1)

// MA #2
show_ma2    = input.bool(false, t_ma2, inline=t_ma2, group=gr_ma)
ma2_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma2, group=gr_ma)
ma2_source  = input.source(close, '', inline=t_ma2, group=gr_ma)
ma2_length  = input.int(100, '', minval=1, inline=t_ma2, group=gr_ma)
ma2_color   = #ff7b5a
ma2         = f_ma(ma2_source, ma2_length, ma2_type)
plot(show_ma2 ? ma2 : na, color = color.new(ma2_color, 0), title = t_ma2, linewidth = 1)

// MA #3
show_ma3    = input.bool(false, t_ma3, inline=t_ma3, group=gr_ma)
ma3_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma3, group=gr_ma)
ma3_source  = input.source(close, '', inline=t_ma3, group=gr_ma)
ma3_length  = input.int(200, '', minval=1, inline=t_ma3, group=gr_ma)
ma3_color   = #ffc100
ma3         = f_ma(ma3_source, ma3_length, ma3_type)
plot(show_ma3 ? ma3 : na, color = color.new(ma3_color, 0), title = t_ma3, linewidth = 1)

// ================================================================================================================
// v3: Adding alerts for swing up/down and any swing
// ================================================================================================================

SwingDn     = REMA_up[1] and not(REMA_up)
SwingUp     = REMA_up    and not(REMA_up[1])

alertcondition(SwingUp, ". SALMA Swing Up", "SALMA Swing Up Detected!")                   // explicit swing up
alertcondition(SwingDn, ".. SALMA Swing Down", "SALMA Swing Down Detected!")              // explicit swing down
alertcondition(SwingUp or SwingDn, "... SALMA Swing", "SALMA Swing Detected!")            // Detect any swing


// =================================================================
// Supertrended
srcST = input(close, title='Source', group = g_st)
mav = input.string(title='Moving Average Type', defval='HULL', options=['SMA', 'EMA', 'WMA', 'DEMA', 'TMA', 'VAR', 'WWMA', 'ZLEMA', 'TSF', 'HULL', 'TILL'], group = g_st)
lengthST = input.int(55, 'Moving Average Length', minval=1, group = g_st)
Periods = input(title='ATR Period', defval=10, group = g_st)
Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=0.5, group = g_st)
changeATR = input(title='Change ATR Calculation Method ?', defval=true, group = g_st)
showsignals = input(title='Show Buy/Sell Signals ?', defval=false, group = g_st)
highlighting = input(title='Highlighter On/Off ?', defval=false, group = g_st)



T3a1 = input.float(0.7, 'TILLSON T3 Volume Factor', step=0.1)


Var_Func(srcST, lengthST) =>
    valpha = 2 / (lengthST + 1)
    vud1 = srcST > srcST[1] ? srcST - srcST[1] : 0
    vdd1 = srcST < srcST[1] ? srcST[1] - srcST : 0
    vUD = math.sum(vud1, 9)
    vDD = math.sum(vdd1, 9)
    vCMO = nz((vUD - vDD) / (vUD + vDD))
    VAR = 0.0
    VAR := nz(valpha * math.abs(vCMO) * srcST) + (1 - valpha * math.abs(vCMO)) * nz(VAR[1])
    VAR
VAR = Var_Func(srcST, lengthST)
DEMA = 2 * ta.ema(srcST, lengthST) - ta.ema(ta.ema(srcST, lengthST), lengthST)
Wwma_Func(srcST, lengthST) =>
    wwalpha = 1 / lengthST
    WWMA = 0.0
    WWMA := wwalpha * srcST + (1 - wwalpha) * nz(WWMA[1])
    WWMA
WWMA = Wwma_Func(srcST, lengthST)
Zlema_Func(srcST, lengthST) =>
    zxLag = lengthST / 2 == math.round(lengthST / 2) ? lengthST / 2 : (lengthST - 1) / 2
    zxEMAData = srcST + srcST - srcST[zxLag]
    ZLEMA = ta.ema(zxEMAData, lengthST)
    ZLEMA
ZLEMA = Zlema_Func(srcST, lengthST)
Tsf_Func(srcST, lengthST) =>
    lrc = ta.linreg(srcST, lengthST, 0)
    lrc1 = ta.linreg(srcST, lengthST, 1)
    lrs = lrc - lrc1
    TSF = ta.linreg(srcST, lengthST, 0) + lrs
    TSF
TSF = Tsf_Func(srcST, lengthST)
HMA = ta.wma(2 * ta.wma(srcST, lengthST / 2) - ta.wma(srcST, lengthST), math.round(math.sqrt(lengthST)))
T3e1 = ta.ema(srcST, lengthST)
T3e2 = ta.ema(T3e1, lengthST)
T3e3 = ta.ema(T3e2, lengthST)
T3e4 = ta.ema(T3e3, lengthST)
T3e5 = ta.ema(T3e4, lengthST)
T3e6 = ta.ema(T3e5, lengthST)
T3c1 = -T3a1 * T3a1 * T3a1
T3c2 = 3 * T3a1 * T3a1 + 3 * T3a1 * T3a1 * T3a1
T3c3 = -6 * T3a1 * T3a1 - 3 * T3a1 - 3 * T3a1 * T3a1 * T3a1
T3c4 = 1 + 3 * T3a1 + T3a1 * T3a1 * T3a1 + 3 * T3a1 * T3a1
T3 = T3c1 * T3e6 + T3c2 * T3e5 + T3c3 * T3e4 + T3c4 * T3e3


getMA(srcST, lengthST) =>
    ma = 0.0
    if mav == 'SMA'
        ma := ta.sma(srcST, lengthST)
        ma

    if mav == 'EMA'
        ma := ta.ema(srcST, lengthST)
        ma

    if mav == 'WMA'
        ma := ta.wma(srcST, lengthST)
        ma

    if mav == 'DEMA'
        ma := DEMA
        ma

    if mav == 'TMA'
        ma := ta.sma(ta.sma(srcST, math.ceil(lengthST / 2)), math.floor(lengthST / 2) + 1)
        ma

    if mav == 'VAR'
        ma := VAR
        ma

    if mav == 'WWMA'
        ma := WWMA
        ma

    if mav == 'ZLEMA'
        ma := ZLEMA
        ma

    if mav == 'TSF'
        ma := TSF
        ma

    if mav == 'HULL'
        ma := HMA
        ma

    if mav == 'TILL'
        ma := T3
        ma
    ma

MA = getMA(srcST, lengthST)



atr2 = ta.sma(ta.tr, Periods)
atr = changeATR ? ta.atr(Periods) : atr2
up = MA - Multiplier * atr
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = MA + Multiplier * atr
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
upPlot = plot(trend == 1 ? up : na, title='Up Trend', color=color.new(color.green, 100), linewidth=0, style=plot.style_linebr)
buySignal = trend == 1 and trend[1] == -1
plotshape(buySignal ? up : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.green, 100))
plotshape(buySignal and showsignals ? up : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=color.new(color.green, 0), textcolor=color.new(color.white, 0))
dnPlot = plot(trend == 1 ? na : dn, title='Down Trend', style=plot.style_linebr, linewidth=0, color=color.new(color.red, 100))
sellSignal = trend == -1 and trend[1] == 1
plotshape(sellSignal ? dn : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.red, 100))
plotshape(sellSignal and showsignals ? dn : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=color.new(color.red, 0), textcolor=color.new(color.white, 0))
mPlot = plot(ohlc4, title='', style=plot.style_circles, linewidth=0)
colorup = input.color(defval = color.new(color.green, 60), title = "ColorU", inline = 'color')
colordown = input.color(defval = color.new(color.red, 60), title = "ColorD", inline = 'color')
longFillColor = highlighting ? trend == 1 ? colorup : color.white : color.new(color.white, 100)
shortFillColor = highlighting ? trend == -1 ? colordown : color.white : color.new(color.white, 100)
fill(mPlot, upPlot, title='UpTrend Highligter', color=longFillColor)
fill(mPlot, dnPlot, title='DownTrend Highligter', color=shortFillColor)
alertcondition(buySignal, title='SuperTrend Buy', message='SuperTrend Buy!')
alertcondition(sellSignal, title='SuperTrend Sell', message='SuperTrend Sell!')
changeCond = trend != trend[1]
alertcondition(changeCond, title='SuperTrend Direction Change', message='SuperTrend has changed direction!')

// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Leviathan
// Swing point generation inspired by @BacktestRookies


//Market Structure - By Leviathan
// Constants
color CLEAR = color.rgb(0,0,0,100)

// Inputs
swingSize = input.int(20, 'Swing Length', tooltip='The number of left and right bars checked when searching for a swing point. Higher value = less swing points plotted and lower value = more swing points plotted.', group = g_ms )
bosConfType = input.string('Candle Close', 'BOS Confirmation', ['Candle Close', 'Wicks'], tooltip='Choose whether candle close/wick above previous swing point counts as a BOS.', group = g_ms)
choch = input.bool(true, 'Show CHoCH', tooltip='Renames the first counter trend BOS to CHoCH', group = g_ms )
showSwing = input.bool(true, 'Show Swing Points', tooltip='Show or hide HH, LH, HL, LL', group = g_ms)

showHalf = input.bool(false, 'Show 0.5 Retracement Level', group='0.5 Retracement Level', tooltip='Show a possible 0.5 retracement level between the swing highs and lows of an expansion move.')
halfColor = input.color(color.rgb(41, 39, 176), 'Color', group='0.5 Retracement Level')
halfStyle = input.string('Solid', 'Line Style', ['Solid', 'Dashed', 'Dotted'], group='0.5 Retracement Level')
halfWidth = input.int(1, 'Width', minval=1, group='0.5 Retracement Level')

bosColor = input.color(color.rgb(112, 114, 119), 'Color', group='BOS Settings')
bosStyle = input.string('Dashed', 'Line Style', ['Solid', 'Dashed', 'Dotted'], group='BOS Settings')
bosWidth = input.int(1, 'Width', minval=1, group='BOS Settings')

// Functions
lineStyle(x) =>
    switch x
        'Solid' => line.style_solid
        'Dashed' => line.style_dashed
        'Dotted' => line.style_dotted


// Calculations

//Finding high and low pivots
pivHi = ta.pivothigh(high, swingSize, swingSize)
pivLo = ta.pivotlow(low, swingSize, swingSize)


//Tracking the previous swing levels to determine hh lh hl ll
var float prevHigh = na
var float prevLow = na
var int prevHighIndex = na
var int prevLowIndex = na

//Tracking whether previous levels have been breached
var bool highActive = false
var bool lowActive = false

bool hh = false
bool lh = false
bool hl = false
bool ll = false

//Variable to track the previous swing type, used later on to draw 0.5 Retracement Levels (HH = 2, LH = 1, HL = -1, LL = -2)
var int prevSwing = 0

if not na(pivHi)
    if pivHi >= prevHigh
        hh := true
        prevSwing := 2
    else
        lh := true
        prevSwing := 1
    prevHigh := pivHi
    highActive := true
    prevHighIndex := bar_index - swingSize

if not na(pivLo)
    if pivLo >= prevLow
        hl := true
        prevSwing := -1
    else
        ll := true
        prevSwing := -2
    prevLow := pivLo
    lowActive := true
    prevLowIndex := bar_index - swingSize

//Generating the breakout signals
bool highBroken = false
bool lowBroken = false

//Tracking prev breakout
var int prevBreakoutDir = 0

float highSrc = bosConfType == 'Candle Close' ? close : high
float lowSrc = bosConfType == 'Candle Close' ? close : low

if highSrc > prevHigh and highActive
    highBroken := true
    highActive := false
if lowSrc < prevLow and lowActive
    lowBroken := true
    lowActive := false


// Visual Output

//Swing level labels
if hh and showSwing
    label.new(bar_index - swingSize, pivHi, 'HH', color=CLEAR, style=label.style_label_down, textcolor=chart.fg_color)
    //Detecting if it is a hh after a hl
    if prevSwing[1] == -1 and showHalf
        line.new(prevLowIndex, (prevLow + pivHi) / 2, bar_index - swingSize, (prevLow + pivHi) / 2, color=halfColor, style=lineStyle(halfStyle), width=halfWidth)
if lh and showSwing
    label.new(bar_index - swingSize, pivHi, 'LH', color=CLEAR, style=label.style_label_down, textcolor=chart.fg_color)
if hl and showSwing
    label.new(bar_index - swingSize, pivLo, 'HL', color=CLEAR, style=label.style_label_up, textcolor=chart.fg_color)
if ll and showSwing
    label.new(bar_index - swingSize, pivLo, 'LL', color=CLEAR, style=label.style_label_up, textcolor=chart.fg_color)
    //Detecting if it is a ll after a lh
    if prevSwing[1] == 1 and showHalf
        line.new(prevHighIndex, (prevHigh + pivLo) / 2, bar_index - swingSize, (prevHigh + pivLo) / 2, color=halfColor, style=lineStyle(halfStyle), width=halfWidth)

//Generating the BOS Lines
if highBroken
    line.new(prevHighIndex, prevHigh, bar_index, prevHigh, color=bosColor, style=lineStyle(bosStyle), width=bosWidth)
    label.new(math.floor(bar_index - (bar_index - prevHighIndex) / 2), prevHigh, prevBreakoutDir == -1 and choch ? 'CHoCH' : 'BOS', color=CLEAR, textcolor=bosColor, size=size.tiny)
    prevBreakoutDir := 1
if lowBroken
    line.new(prevLowIndex, prevLow, bar_index, prevLow, color=bosColor, style=lineStyle(bosStyle), width=bosWidth)
    label.new(math.floor(bar_index - (bar_index - prevLowIndex) / 2), prevLow, prevBreakoutDir == 1 and choch ? 'CHoCH' : 'BOS', color=CLEAR, textcolor=bosColor, style=label.style_label_up, size=size.tiny)
    prevBreakoutDir := -1