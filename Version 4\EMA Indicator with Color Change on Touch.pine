//@version=4
study("EMA Indicator with Color Change on Touch", shorttitle="EMA", overlay=true)

// EMA 20
length20 = input(20, title="EMA 20 Period")
ema20 = ema(close, length20)
ema20Color = ema20[1] > low ? color.red : ema20[1] < high ? color.green : na
plot(ema20, color=ema20Color, title="EMA 20")

// EMA 50
length50 = input(50, title="EMA 50 Period")
ema50 = ema(close, length50)
ema50Color = ema50[1] > low ? color.red : ema50[1] < high ? color.yellow : na
plot(ema50, color=ema50Color, title="EMA 50")

// EMA 200
length200 = input(200, title="EMA 200 Period")
ema200 = ema(close, length200)
ema200Color = ema200[1] > low ? color.red : ema200[1] < high ? color.orange : na
plot(ema200, color=ema200Color, title="EMA 200")
