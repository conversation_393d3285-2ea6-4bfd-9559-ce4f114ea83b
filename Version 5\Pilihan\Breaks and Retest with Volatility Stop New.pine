//@version=5
indicator("Breaks and Retests, Volatility Stop and Trendline", 'BR + VS + TL', overlay = true, max_boxes_count = 500, max_labels_count = 500)

g_sr = 'Support and Resistance'
g_c  = 'Conditions'
g_st = 'Styling'
g_tl = 'Trendline'
g_vs = 'Volatility Stop'
t_r  = 'Bar Confirmation: Generates alerts when candle closes. (1 Candle Later) \n\nHigh & Low: By default, the Break & Retest system uses the current close value to determine a condition, selecting High & Low will make the script utilize these two values instead of the close value. In return, the script won\'t repaint and will yield different results.'
t_rv = 'Whenever a potential retest is detected, the indicator knows that a retest is about to happen. In that given situation, this input grants the ability to raise the limit on how many bars are allowed to be actively checked while a potential retest event is active.\n\nExample, if you see the potential retest label, how many bars do you want that potential retest label to be active for to eventually confirm a retest? This system was implemented to prevent retest alerts from going off 10+ bars later from the potential retest point leading to inaccurate results.'

input_lookback  = input.int(defval = 20, title = 'Lookback Range', minval = 1, tooltip = 'How many bars for a pivot event to occur.', group = g_sr)
input_retSince  = input.int(defval = 2, title = 'Bars Since Breakout', minval = 1, tooltip = 'How many bars since breakout in order to detect a retest.', group = g_sr)
input_retValid  = input.int(defval = 2, title = 'Retest Detection Limiter', minval = 1, tooltip = t_rv, group = g_sr)
input_breakout  = input.bool(defval = true, title = 'Breakouts', group = g_c)
input_retest    = input.bool(defval = true, title = 'Retests', group = g_c)
input_repType   = input.string(defval = 'On', title = 'Repainting', options = ['On', 'Off: Candle Confirmation', 'Off: High & Low'], tooltip = t_r, group = g_c)
input_outL      = input.string(defval = line.style_dotted, title = 'Outline', group = g_st, options = [line.style_dotted, line.style_dashed, line.style_solid])
input_extend    = input.string(defval = extend.none, title = 'Extend', group = g_st, options = [extend.none, extend.right, extend.left, extend.both])
input_labelType = input.string(defval = 'Full', title = 'Label Type', options = ['Full', 'Simple'], group = g_st)
input_labelSize = input.string(defval = size.small, title = 'Label Size', options = [size.tiny, size.small, size.normal, size.large, size.huge], group = g_st)
input_plColor   = input.color(defval = color.red, title = 'Support', inline = 'Color', group = g_st)
input_phColor   = input.color(defval = #089981, title = 'Resistance', inline = 'Color', group = g_st)
input_override  = input.bool(defval = false, title = 'Override Text Color ', inline = 'Override', group = g_st)
input_textColor = input.color(defval = color.white, title = '', inline = 'Override', group = g_st)
bb              = input_lookback

rTon            = input_repType == 'On'
rTcc            = input_repType == 'Off: Candle Confirmation'
rThv            = input_repType == 'Off: High & Low'
breakText       = input_labelType == 'Simple' ? 'Br' : 'Break'

// Pivot Instance
pl = fixnan(ta.pivotlow(low, bb, bb))
ph = fixnan(ta.pivothigh(high, bb, bb))

// Box Height
s_yLoc = low[bb + 1] > low[bb - 1] ? low[bb - 1] : low[bb + 1]
r_yLoc = high[bb + 1] > high[bb - 1] ? high[bb + 1] : high[bb - 1]

//-----------------------------------------------------------------------------
// Functions
//-----------------------------------------------------------------------------
drawBox(condition, y1, y2, color) =>
    var box drawBox = na
    if condition
        box.set_right(drawBox, bar_index - bb)
        drawBox.set_extend(extend.none)
        drawBox := box.new(bar_index - bb, y1, bar_index, y2, color, bgcolor = color.new(color, 90), border_style = input_outL, extend = input_extend)
    [drawBox]

updateBox(box) =>
    if barstate.isconfirmed
        box.set_right(box, bar_index + 5)

breakLabel(y, color, style, textform) => label.new(bar_index, y, textform, textcolor = input_override ? input_textColor : color, style = style, color = color.new(color, 50), size = input_labelSize)
retestCondition(breakout, condition) => ta.barssince(na(breakout)) > input_retSince and condition
repaint(c1, c2, c3) => rTon ? c1 : rThv ? c2 : rTcc ? c3 : na

//-----------------------------------------------------------------------------
// Draw and Update Boxes
//-----------------------------------------------------------------------------
[sBox] = drawBox(ta.change(pl), s_yLoc, pl, input_plColor)
[rBox] = drawBox(ta.change(ph), ph, r_yLoc, input_phColor)
sTop = box.get_top(sBox), rTop = box.get_top(rBox)
sBot = box.get_bottom(sBox), rBot = box.get_bottom(rBox)

updateBox(sBox), updateBox(rBox)

//-----------------------------------------------------------------------------
// Breakout Event
//-----------------------------------------------------------------------------
var bool sBreak = na
var bool rBreak = na
cu = repaint(ta.crossunder(close, box.get_bottom(sBox)), ta.crossunder(low, box.get_bottom(sBox)), ta.crossunder(close, box.get_bottom(sBox)) and barstate.isconfirmed)
co = repaint(ta.crossover(close, box.get_top(rBox)), ta.crossover(high, box.get_top(rBox)), ta.crossover(close, box.get_top(rBox)) and barstate.isconfirmed)

switch
    cu and na(sBreak) =>
        sBreak := true
        if input_breakout
            breakLabel(sBot, input_plColor, label.style_label_upper_right, breakText)
    co and na(rBreak) =>
        rBreak := true
        if input_breakout
            breakLabel(rTop, input_phColor, label.style_label_lower_right, breakText)

if ta.change(pl)
    if na(sBreak)
        box.delete(sBox[1])
    sBreak := na
if ta.change(ph)
    if na(rBreak)
        box.delete(rBox[1])
    rBreak := na

//-----------------------------------------------------------------------------
// Retest Event
//-----------------------------------------------------------------------------
s1 = retestCondition(sBreak, high >= sTop and close <= sBot)                                            // High is GOET top sBox value and the close price is LOET the bottom sBox value.
s2 = retestCondition(sBreak, high >= sTop and close >= sBot and close <= sTop)                          // High is GOET top sBox value and close is GOET the bottom sBox value and closing price is LOET the top sBox value.
s3 = retestCondition(sBreak, high >= sBot and high <= sTop)                                             // High is in between the sBox.
s4 = retestCondition(sBreak, high >= sBot and high <= sTop and close < sBot)                            // High is in between the sBox, and the closing price is below.

r1 = retestCondition(rBreak, low <= rBot and close >= rTop)                                             // Low is LOET bottom rBox value and close is GOET the top sBox value
r2 = retestCondition(rBreak, low <= rBot and close <= rTop and close >= rBot)                           // Low is LOET bottom rBox value and close is LOET the top sBox value and closing price is GOET the bottom rBox value.
r3 = retestCondition(rBreak, low <= rTop and low >= rBot)                                               // Low is in between the rBox.
r4 = retestCondition(rBreak, low <= rTop and low >= rBot and close > rTop)                              // Low is in between the rBox, and the closing price is above.

retestEvent(c1, c2, c3, c4, y1, y2, col, style, pType) =>
    if input_retest
        var bool retOccurred = na
        retActive   = c1 or c2 or c3 or c4
        retEvent    = retActive and not retActive[1]
        retValue    = ta.valuewhen(retEvent, y1, 0)

        if pType == 'ph' ? y2 < ta.valuewhen(retEvent, y2, 0) : y2 > ta.valuewhen(retEvent, y2, 0)
            retEvent := retActive

        // Must be reassigned here just in case the above if statement triggers.
        retValue := ta.valuewhen(retEvent, y1, 0)

        retSince = ta.barssince(retEvent)
        var retLabel = array.new<label>()

        if retEvent
            retOccurred := na
            array.push(retLabel, label.new(bar_index - retSince, y2[retSince], text = input_labelType == 'Simple' ? 'P. Re' : 'Potential Retest', color = color.new(col, 50), style = style, textcolor = input_override ? input_textColor : col, size = input_labelSize))

        if array.size(retLabel) == 2
            label.delete(array.first(retLabel))
            array.shift(retLabel)

        retConditions = pType == 'ph' ? repaint(close >= retValue, high >= retValue, close >= retValue and barstate.isconfirmed) : repaint(close <= retValue, low <= retValue, close <= retValue and barstate.isconfirmed)
        retValid = ta.barssince(retEvent) > 0 and ta.barssince(retEvent) <= input_retValid and retConditions and not retOccurred

        if retValid
            label.new(bar_index - retSince, y2[retSince], text = input_labelType == 'Simple' ? 'Re' : 'Retest', color = color.new(col, 50), style = style, textcolor = input_override ? input_textColor : col, size = input_labelSize)
            retOccurred := true

        if retValid or ta.barssince(retEvent) > input_retValid
            label.delete(array.first(retLabel))

        if pType == 'ph' and ta.change(ph) and retOccurred
            box.set_right(rBox[1], bar_index - retSince)
            retOccurred := na

        if pType == 'pl' and ta.change(pl) and retOccurred
            box.set_right(sBox[1], bar_index - retSince)
            retOccurred := na
        [retValid, retEvent, retValue]

[rRetValid, rRetEvent] = retestEvent(r1, r2, r3, r4, high, low, input_phColor, label.style_label_upper_left, 'ph')
[sRetValid, sRetEvent] = retestEvent(s1, s2, s3, s4, low, high, input_plColor, label.style_label_lower_left, 'pl')

//-----------------------------------------------------------------------------
// Alerts
//-----------------------------------------------------------------------------
alertcondition(ta.change(pl), 'New Support Level')
alertcondition(ta.change(ph), 'New Resistance Level')
alertcondition(ta.barssince(na(sBreak)) == 1, 'Support Breakout')
alertcondition(ta.barssince(na(rBreak)) == 1, 'Resistance Breakout')
alertcondition(sRetValid, 'Support Retest')
alertcondition(sRetEvent, 'Potential Support Retest')
alertcondition(rRetValid, 'Resistance Retest')
alertcondition(rRetEvent, 'Potential Resistance Retest')

AllAlerts(condition, message) =>
    if condition
        alert(message)

AllAlerts(ta.change(pl), 'New Support Level')
AllAlerts(ta.change(ph), 'New Resistance Level')
AllAlerts(ta.barssince(na(sBreak)) == 1, 'Support Breakout')
AllAlerts(ta.barssince(na(rBreak)) == 1, 'Resistance Breakout')
AllAlerts(sRetValid, 'Support Retest')
AllAlerts(sRetEvent, 'Potential Support Retest')
AllAlerts(rRetValid, 'Resistance Retest')
AllAlerts(rRetEvent, 'Potential Resistance Retest')

// Volatility Stop indicator code
length = input.int(20, 'Volatility Stop Length', minval=2, group = g_vs)
src = input(close, 'Volatility Stop Source', group = g_vs)
factor = input.float(2.0, 'Volatility Stop Multiplier', minval=0.25, step=0.25, group = g_vs)
Barcolor = input(true, group = g_vs)
showBuySell = input(true, 'Show Buy/Sell Signals', group = g_vs)

volStop(src, atrlen, atrfactor) =>
    var max = src
    var min = src
    var uptrend = true
    var stop = 0.0
    atrM = ta.atr(atrlen) * atrfactor
    max := math.max(max, src)
    min := math.min(min, src)
    stop := nz(uptrend ? math.max(stop, max - atrM) : math.min(stop, min + atrM), src)
    uptrend := src - stop >= 0.0
    if uptrend != nz(uptrend[1], true)
        max := src
        min := src
        stop := uptrend ? max - atrM : min + atrM
        stop
    [stop, uptrend]

[vStop, uptrend] = volStop(src, length, factor)

plot(vStop, 'Volatility Stop', color=uptrend ? color.new(#007F0E, 0) : color.new(#872323, 0), linewidth=2)

iff_1 = close < vStop ? color.new(#FF0000, 0) : color.new(color.black, 0)
colors = close > vStop ? color.new(#008000, 0) : iff_1

barcolor(Barcolor ? colors : na)

Buy = ta.crossover(close, vStop)
Sell = ta.crossunder(close, vStop)

plotshape(showBuySell and Buy, 'BUY', shape.labelup, location.belowbar, color=color.new(color.green, 0), text='BUY', textcolor=color.new(color.black, 0))
plotshape(showBuySell and Sell, 'SELL', shape.labeldown, location.abovebar, color=color.new(color.red, 0), text='SELL', textcolor=color.new(color.black, 0))

alertcondition(Buy, 'Buy Signal', 'Buy ATR Trailing Stop')
alertcondition(Sell, 'Sell Signal', 'Sell ATR Trailing Stop')

// Adjustable Moving Averages
input_ma1_length = input.int(defval = 20, title = 'MA 1 Length', minval = 1, group = g_sr)
input_ma2_length = input.int(defval = 50, title = 'MA 2 Length', minval = 1, group = g_sr)
// input_ma1_color = input.color(defval = color.rgb(33, 149, 243, 50), title = 'MA 1 Color', group = g_sr)
input_ma1_color = input.color(defval = color.green, title = 'MA 1 Color Uptrend', group = g_sr)
input_ma1_color2 = input.color(defval = color.red, title = 'MA 1 Color Downtrend', group = g_sr)
// input_ma2_color = input.color(defval = color.rgb(255, 153, 0, 50), title = 'MA 2 Color', group = g_sr)
input_ma2_color = input.color(defval = color.yellow, title = 'MA 2 Color Uptrend', group = g_sr)
input_ma2_color2 = input.color(defval = color.red, title = 'MA 2 Color Downtrend', group = g_sr)

// Calculate Moving Averages
ma1 = ta.sma(close, input_ma1_length)
ma2 = ta.sma(close, input_ma2_length)

// Plotting Moving Averages
// plot(ma1, "MA 1", color=input_ma1_color, linewidth=1)
plot(ma1, color=close[1] > ma1 and close > ma1 ? input_ma1_color : input_ma1_color2, linewidth=2, title="MA 1")
// plot(ma2, "MA 2", color=input_ma2_color, linewidth=1)
plot(ma2, color=close[1] > ma2 and close > ma2 ? input_ma2_color : input_ma2_color2, linewidth=2, title="MA 2")

//Trend Line
// User inputs
prd = input.int(defval=1, title=' Period for Pivot Points (Trendline)', minval=1, maxval=50, group = g_tl)
max_num_of_pivots = input.int(defval=6, title=' Maximum Number of Pivots (Trendline)', minval=5, maxval=10, group = g_tl)
max_lines = input.int(defval=1, title=' Maximum number of trend lines (Trendline)', minval=1, maxval=10, group = g_tl)
show_lines = input.bool(defval=true, title=' Show trend lines (Trendline)', group = g_tl)
show_pivots = input.bool(defval=false, title=' Show Pivot Points (Trendline)', group = g_tl)
sup_line_color = input(defval = color.lime, title = "Colors (Trendline)", inline = "tcol", group = g_tl)
res_line_color = input(defval = color.red, title = "", inline = "tcol", group = g_tl)

float p_h = ta.pivothigh(high, prd, prd)
float p_l = ta.pivotlow(low, prd, prd)

plotshape(p_h and show_pivots, style=shape.triangledown, location=location.abovebar, offset=-prd, size=size.tiny)
plotshape(p_l and show_pivots, style=shape.triangleup, location=location.belowbar, offset=-prd, size=size.tiny)

// Creating array of pivots
var pivots_high = array.new_float(0)
var pivots_low = array.new_float(0)

var high_ind = array.new_int(0)
var low_ind = array.new_int(0)

if p_h
    array.push(pivots_high, p_h)
    array.push(high_ind, bar_index - prd)
    if array.size(pivots_high) > max_num_of_pivots  // limit the array size
        array.shift(pivots_high)
        array.shift(high_ind)

if p_l
    array.push(pivots_low, p_l)
    array.push(low_ind, bar_index - prd)
    if array.size(pivots_low) > max_num_of_pivots  // limit the array size
        array.shift(pivots_low)
        array.shift(low_ind)

// Create arrays to store slopes and lines
var res_lines = array.new_line()
var res_slopes = array.new_float()

len_lines = array.size(res_lines)

if (len_lines >= 1)
    for ind = 0 to len_lines - 1
        to_delete = array.pop(res_lines)
        array.pop(res_slopes)
        line.delete(to_delete)


count_slope(p_h1, p_h2, pos1, pos2) => (p_h2 - p_h1) / (pos2 - pos1)


if array.size(pivots_high) == max_num_of_pivots
    index_of_biggest_slope = 0
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_high, ind1)
            p2 = array.get(pivots_high, ind2)
            pos1 = array.get(high_ind, ind1)
            pos2 = array.get(high_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_high, ind3)
                    pos3 = array.get(high_ind, ind3)
                    if p3 > k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] > p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k


            if ok
                if array.size(res_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                    array.push(res_lines, line)
                    array.push(res_slopes, k)
                else
                    max_slope = array.max(res_slopes)
                    max_slope_ind = array.indexof(res_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k < max_slope
                        line_to_delete = array.get(res_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                        array.insert(res_lines, max_slope_ind, new_line)
                        array.insert(res_slopes, max_slope_ind, k)
                        array.remove(res_lines, max_slope_ind + 1)
                        array.remove(res_slopes, max_slope_ind + 1)

if not show_lines
    len_l = array.size(res_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(res_lines)
            array.pop(res_slopes)
            line.delete(to_delete)



var sup_lines = array.new_line()
var sup_slopes = array.new_float()

len_lines1 = array.size(sup_lines)

if (len_lines1 >= 1)
    for ind = 0 to len_lines1 - 1
        to_delete = array.pop(sup_lines)
        array.pop(sup_slopes)
        line.delete(to_delete)

if array.size(pivots_low) == max_num_of_pivots
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_low, ind1)
            p2 = array.get(pivots_low, ind2)
            pos1 = array.get(low_ind, ind1)
            pos2 = array.get(low_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            // check if pivot points in the middle of two points is lower
            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_low, ind3)
                    pos3 = array.get(low_ind, ind3)
                    if p3 < k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] < p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k

            if ok
                if array.size(sup_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                    array.push(sup_lines, line)
                    array.push(sup_slopes, k)
                else
                    max_slope = array.min(sup_slopes)
                    max_slope_ind = array.indexof(sup_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k > max_slope
                        line_to_delete = array.get(sup_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                        array.insert(sup_lines, max_slope_ind, new_line)
                        array.insert(sup_slopes, max_slope_ind, k)
                        array.remove(sup_lines, max_slope_ind + 1)
                        array.remove(sup_slopes, max_slope_ind + 1)


if not show_lines
    len_l = array.size(sup_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(sup_lines)
            array.pop(sup_slopes)
            line.delete(to_delete)