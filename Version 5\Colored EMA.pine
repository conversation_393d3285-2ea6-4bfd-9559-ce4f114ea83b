// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © ZenAndTheArtOfTrading / PineScriptMastery
// Last Updated: 1st February, 2022
// @version=5
indicator(title="Colored EMA", shorttitle="EMA+", overlay=true)

// Script settings
emaLength1 = input.int(title="EMA Length 1", defval=20, minval=2)
emaLength2 = input.int(title="EMA Length 2", defval=50, minval=2)
emaLength3 = input.int(title="EMA Length 3", defval=200, minval=2)
emaSource1 = input.source(title="EMA Source 1", defval=close)
emaSource2 = input.source(title="EMA Source 2", defval=close)
emaSource3 = input.source(title="EMA Source 3", defval=close)

// Get EMA values for different lengths
ema1 = ta.ema(emaSource1, emaLength1)
ema2 = ta.ema(emaSource2, emaLength2)
ema3 = ta.ema(emaSource3, emaLength3)

// Plot EMAs
plot(ema1, color=close[1] > ema1 and close > ema1 ? color.green : color.red, linewidth=2, title="EMA 1")
plot(ema2, color=close[1] > ema2 and close > ema2 ? color.yellow : color.red, linewidth=2, title="EMA 2")
plot(ema3, color=close[1] > ema3 and close > ema3 ? color.orange : color.red, linewidth=2, title="EMA 3")
