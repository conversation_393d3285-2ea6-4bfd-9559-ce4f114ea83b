//@version=6
indicator('M2 Global Liquidity Index - X Days Lead + EMA', overlay=true, scale=scale.left)

// Define offset in days as user input with detailed tooltip
offset_days = input.int(84, "Offset Days", minval=1, step=1, tooltip="Number of days to offset the data (e.g., 70 = 10 weeks/84 = 12 weeks).\n\nTrader Jangka Pendek: Jika Anda fokus pada pergerakan mingguan atau bulanan, offset seperti 10 minggu (70 hari) atau 12 minggu (84 hari) mungkin cukup.\n\nTrader Jangka Menengah: Untuk melihat tren 3-6 bulan ke depan, 108 hari (15 minggu + 3 hari) atau 180 hari (25 minggu + 5 hari) bisa jadi pilihan.\n\nInvestor Jangka Panjang: Jika Anda ingin melihat dampak likuiditas dalam setahun, 360 hari (51 minggu + 3 hari) mungkin lebih relevan.")

// Define EMA periods as user inputs
ema_short_period = input.int(20, "EMA Short Period", minval=1, step=1, tooltip="Short-term EMA period (e.g., 20)")
ema_mid_period = input.int(50, "EMA Mid Period", minval=1, step=1, tooltip="Mid-term EMA period (e.g., 50)")
ema_long_period = input.int(100, "EMA Long Period", minval=1, step=1, tooltip="Long-term EMA period (e.g., 100)")

// Convert days to milliseconds (1 day = 86400000 ms)
offset_ms = offset_days * 86400000

cnm2   = request.security("ECONOMICS:CNM2", "D", close)
cnyusd = request.security("FX_IDC:CNYUSD", "D", close)

usm2 = request.security("ECONOMICS:USM2", "D", close)

eum2 = request.security("ECONOMICS:EUM2", "D", close)
eurusd = request.security("FX:EURUSD", "D", close)

jpm2 = request.security("ECONOMICS:JPM2", "D", close)
jpyusd = request.security("FX_IDC:JPYUSD", "D", close)

gbm2 = request.security("ECONOMICS:GBM2", "D", close)
gbpusd = request.security("FX:GBPUSD", "D", close)

total = (cnm2 * cnyusd + usm2 + eum2 * eurusd + jpm2 * jpyusd + gbm2 * gbpusd) / 1000000000000

// Calculate EMAs based on the total liquidity index
ema_short = ta.ema(total, ema_short_period)
ema_mid = ta.ema(total, ema_mid_period)
ema_long = ta.ema(total, ema_long_period)

// Determine if the bar is in the future relative to the current time with the offset
is_future = bar_index >= last_bar_index - offset_days

// Plot the total liquidity index (Past/Present and Future)
plot(not is_future ? total : na, color=color.rgb(243, 93, 19), linewidth=2, offset=offset_days, title="Past/Present")
plot(is_future ? total : na, color=color.rgb(104, 100, 98), linewidth=2, offset=offset_days, title="Future")

// Plot the EMAs with different colors, extending to the end of offset_days
plot(ema_short, color=color.blue, linewidth=1, offset=offset_days, title="EMA Short")
plot(ema_mid, color=color.yellow, linewidth=1, offset=offset_days, title="EMA Mid")
plot(ema_long, color=color.purple, linewidth=1, offset=offset_days, title="EMA Long")