//@version=6
indicator('M2 Global Liquidity Index - X Days Lead', overlay=true, scale=scale.left)

// Define offset in days as user input
offset_days = input.int(84, "Offset Days", minval=1, step=1, tooltip="Number of days to offset the data (e.g., 70 = 10 weeks/84 = 12 weeks)")

// Convert days to milliseconds (1 day = 86400000 ms)
offset_ms = offset_days * 86400000

cnm2   = request.security("ECONOMICS:CNM2", "D", close)
cnyusd = request.security("FX_IDC:CNYUSD", "D", close)

usm2 = request.security("ECONOMICS:USM2", "D", close)

eum2 = request.security("ECONOMICS:EUM2", "D", close)
eurusd = request.security("FX:EURUSD", "D", close)

jpm2 = request.security("ECONOMICS:JPM2", "D", close)
jpyusd = request.security("FX_IDC:JPYUSD", "D", close)

gbm2 = request.security("ECONOMICS:GBM2", "D", close)
gbpusd = request.security("FX:GBPUSD", "D", close)

total = (cnm2 * cnyusd + usm2 + eum2 * eurusd + jpm2 * jpyusd + gbm2 * gbpusd) / 1000000000000

// Determine if the bar is in the future relative to the current time with the offset
is_future = bar_index >= last_bar_index - offset_days

// Plot two lines with different colors
plot(not is_future ? total : na, color=color.rgb(243, 93, 19), linewidth=2, offset=offset_days, title="Past/Present")
plot(is_future ? total : na, color=color.rgb(104, 100, 98), linewidth=2, offset=offset_days, title="Future")