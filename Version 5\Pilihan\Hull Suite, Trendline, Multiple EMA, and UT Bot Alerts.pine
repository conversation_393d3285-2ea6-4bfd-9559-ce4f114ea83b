//@version=5

indicator('Hull Suite, Trendline, Multiple EMA, and UT Bot Alerts', overlay=true)
g_sema = 'Show EMA'
g_ema = 'EMA'
g_shs = 'Show Hull Suite'
g_hs = 'Hull Suite'
g_subt = 'Show UT Bot Alerts'
g_ubt = 'UT Bot Alerts'
g_stl = 'Show Trendline'
g_tl = 'Trendline'
// Checkbox for showing/hiding EMA Line
showEMA1 = input(false, title="Show EMA 1?", group = g_sema)
showEMA2 = input(false, title="Show EMA 2?", group = g_sema)
showEMA3 = input(false, title="Show EMA 3?", group = g_sema)
showEMA4 = input(false, title="Show EMA 4?", group = g_sema)
showEMA5 = input(true, title="Show EMA 5?", group = g_sema)
// Checkbox for showing/hiding UT Bot Alert signals
showAlerts = input(true, title="Show UT Bot Alerts", group = g_subt)
// Checkbox for showing/hiding Hull Suite
showHullSuite = input(true, title="Show Hull Suite?", group = g_shs)
visualSwitch = input(true, title='Show as a Band? (Hull Suite)', group = g_shs)

// Hull Suite
//INPUT
src = input(close, title='Source', group = g_hs)
modeSwitch = input.string('Hma', title='Hull Variation', options=['Hma', 'Thma', 'Ehma'], group = g_hs)
length = input(55, title='Length(180-200 for floating S/R , 55 for swing entry)', group = g_hs)
lengthMult = input(1.0, title='Length multiplier (Used to view higher timeframes with straight band)', group = g_hs)

useHtf = input(false, title='Show Hull MA from X timeframe? (good for scalping)', group = g_hs)
htf = input.timeframe('240', title='Higher timeframe', group = g_hs)

switchColor = input(true, 'Color Hull according to trend?', group = g_hs)
candleCol = input(false, title='Color candles based on Hull\'s Trend?', group = g_hs)
thicknesSwitch = input(1, title='Line Thickness', group = g_hs)
transpSwitch = input.int(40, title='Band Transparency', step=5, group = g_hs)

//FUNCTIONS
//HMA
HMA(_src, _length) =>
    ta.wma(2 * ta.wma(_src, _length / 2) - ta.wma(_src, _length), math.round(math.sqrt(_length)))
//EHMA
EHMA(_src, _length) =>
    ta.ema(2 * ta.ema(_src, _length / 2) - ta.ema(_src, _length), math.round(math.sqrt(_length)))
//THMA
THMA(_src, _length) =>
    ta.wma(ta.wma(_src, _length / 3) * 3 - ta.wma(_src, _length / 2) - ta.wma(_src, _length), _length)

//SWITCH
Mode(modeSwitch, src, len) =>
    modeSwitch == 'Hma' ? HMA(src, len) : modeSwitch == 'Ehma' ? EHMA(src, len) : modeSwitch == 'Thma' ? THMA(src, len / 2) : na

//OUT
_hull = Mode(modeSwitch, src, int(length * lengthMult))
HULL = useHtf ? request.security(syminfo.ticker, htf, _hull) : _hull
MHULL = HULL[0]
SHULL = HULL[2]

//COLOR
hullColor = switchColor ? HULL > HULL[2] ? #00ff00 : #ff0000 : #ff9800

//PLOT
///< Frame
Fi1 = plot(showHullSuite ? MHULL : na, title='MHULL', color=hullColor, linewidth=thicknesSwitch, transp=50)
Fi2 = plot(visualSwitch ? SHULL : na, title='SHULL', color=hullColor, linewidth=thicknesSwitch, transp=50)
alertcondition(ta.crossover(MHULL, SHULL), title='Hull trending up.', message='Hull trending up.')
alertcondition(ta.crossover(SHULL, MHULL), title='Hull trending down.', message='Hull trending down.')
///< Ending Filler
fill(Fi1, Fi2, title='Band Filler', color=hullColor, transp=transpSwitch)
///BARCOLOR
barcolor(color=candleCol ? switchColor ? hullColor : na : na)


// UT BOT ALERT
// Inputs
a = input(3, title='Key Value (UT Bot Alert). \'This changes the sensitivity\'', group = g_ubt)
c = input(10, title='ATR Period (UT Bot Alert)', group = g_ubt)
h = input(false, title='Signals from Heikin Ashi Candles (UT Bot Alert)', group = g_ubt)


xATR = ta.atr(c)
nLoss = a * xATR

srcUt = h ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, close, lookahead=barmerge.lookahead_off) : close

xATRTrailingStop = 0.0
iff_1 = srcUt > nz(xATRTrailingStop[1], 0) ? srcUt - nLoss : srcUt + nLoss
iff_2 = srcUt < nz(xATRTrailingStop[1], 0) and srcUt[1] < nz(xATRTrailingStop[1], 0) ? math.min(nz(xATRTrailingStop[1]), srcUt + nLoss) : iff_1
xATRTrailingStop := srcUt > nz(xATRTrailingStop[1], 0) and srcUt[1] > nz(xATRTrailingStop[1], 0) ? math.max(nz(xATRTrailingStop[1]), srcUt - nLoss) : iff_2

pos = 0
iff_3 = srcUt[1] > nz(xATRTrailingStop[1], 0) and srcUt < nz(xATRTrailingStop[1], 0) ? -1 : nz(pos[1], 0)
pos := srcUt[1] < nz(xATRTrailingStop[1], 0) and srcUt > nz(xATRTrailingStop[1], 0) ? 1 : iff_3

xcolor = pos == -1 ? color.red : pos == 1 ? color.green : color.blue

ema = ta.ema(srcUt, 1)
above = ta.crossover(ema, xATRTrailingStop)
below = ta.crossover(xATRTrailingStop, ema)

buy = srcUt > xATRTrailingStop and above
sell = srcUt < xATRTrailingStop and below

barbuy = srcUt > xATRTrailingStop
barsell = srcUt < xATRTrailingStop

// Plot UT Bot Alerts with checkbox
plotshape(showAlerts and buy, title='Buy (UT Bot Alert)', text='Buy', style=shape.labelup, location=location.belowbar, color=color.new(color.green, 0), textcolor=color.new(color.white, 0), size=size.tiny)
plotshape(showAlerts and sell, title='Sell (UT Bot Alert)', text='Sell', style=shape.labeldown, location=location.abovebar, color=color.new(color.red, 0), textcolor=color.new(color.white, 0), size=size.tiny)

// Hide Bar Color
// barcolor(barbuy ? color.green : na, title='Bar Color (UT Bot Alert)')
// barcolor(barsell ? color.red : na, title='Bar Color (UT Bot Alert)')

alertcondition(buy, 'UT Long', 'UT Long')
alertcondition(sell, 'UT Short', 'UT Short')


//Trend Line
// User inputs
prd = input.int(defval=5, title=' Period for Pivot Points (Trendline)', minval=1, maxval=50, group = g_tl)
max_num_of_pivots = input.int(defval=6, title=' Maximum Number of Pivots (Trendline)', minval=5, maxval=10, group = g_tl)
max_lines = input.int(defval=1, title=' Maximum number of trend lines (Trendline)', minval=1, maxval=10, group = g_tl)
show_lines = input.bool(defval=true, title=' Show trend lines (Trendline)', group = g_tl)
show_pivots = input.bool(defval=false, title=' Show Pivot Points (Trendline)', group = g_tl)
sup_line_color = input(defval = color.lime, title = "Colors (Trendline)", inline = "tcol", group = g_tl)
res_line_color = input(defval = color.red, title = "", inline = "tcol", group = g_tl)

float p_h = ta.pivothigh(high, prd, prd)
float p_l = ta.pivotlow(low, prd, prd)

plotshape(p_h and show_pivots, style=shape.triangledown, location=location.abovebar, offset=-prd, size=size.tiny)
plotshape(p_l and show_pivots, style=shape.triangleup, location=location.belowbar, offset=-prd, size=size.tiny)

// Creating array of pivots
var pivots_high = array.new_float(0)
var pivots_low = array.new_float(0)

var high_ind = array.new_int(0)
var low_ind = array.new_int(0)

if p_h
    array.push(pivots_high, p_h)
    array.push(high_ind, bar_index - prd)
    if array.size(pivots_high) > max_num_of_pivots  // limit the array size
        array.shift(pivots_high)
        array.shift(high_ind)

if p_l
    array.push(pivots_low, p_l)
    array.push(low_ind, bar_index - prd)
    if array.size(pivots_low) > max_num_of_pivots  // limit the array size
        array.shift(pivots_low)
        array.shift(low_ind)

// Create arrays to store slopes and lines
var res_lines = array.new_line()
var res_slopes = array.new_float()

len_lines = array.size(res_lines)

if (len_lines >= 1)
    for ind = 0 to len_lines - 1
        to_delete = array.pop(res_lines)
        array.pop(res_slopes)
        line.delete(to_delete)


count_slope(p_h1, p_h2, pos1, pos2) => (p_h2 - p_h1) / (pos2 - pos1)


if array.size(pivots_high) == max_num_of_pivots
    index_of_biggest_slope = 0
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_high, ind1)
            p2 = array.get(pivots_high, ind2)
            pos1 = array.get(high_ind, ind1)
            pos2 = array.get(high_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_high, ind3)
                    pos3 = array.get(high_ind, ind3)
                    if p3 > k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] > p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k


            if ok
                if array.size(res_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                    array.push(res_lines, line)
                    array.push(res_slopes, k)
                else
                    max_slope = array.max(res_slopes)
                    max_slope_ind = array.indexof(res_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k < max_slope
                        line_to_delete = array.get(res_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                        array.insert(res_lines, max_slope_ind, new_line)
                        array.insert(res_slopes, max_slope_ind, k)
                        array.remove(res_lines, max_slope_ind + 1)
                        array.remove(res_slopes, max_slope_ind + 1)

if not show_lines
    len_l = array.size(res_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(res_lines)
            array.pop(res_slopes)
            line.delete(to_delete)



var sup_lines = array.new_line()
var sup_slopes = array.new_float()

len_lines1 = array.size(sup_lines)

if (len_lines1 >= 1)
    for ind = 0 to len_lines1 - 1
        to_delete = array.pop(sup_lines)
        array.pop(sup_slopes)
        line.delete(to_delete)

if array.size(pivots_low) == max_num_of_pivots
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_low, ind1)
            p2 = array.get(pivots_low, ind2)
            pos1 = array.get(low_ind, ind1)
            pos2 = array.get(low_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            // check if pivot points in the middle of two points is lower
            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_low, ind3)
                    pos3 = array.get(low_ind, ind3)
                    if p3 < k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] < p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k

            if ok
                if array.size(sup_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                    array.push(sup_lines, line)
                    array.push(sup_slopes, k)
                else
                    max_slope = array.min(sup_slopes)
                    max_slope_ind = array.indexof(sup_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k > max_slope
                        line_to_delete = array.get(sup_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                        array.insert(sup_lines, max_slope_ind, new_line)
                        array.insert(sup_slopes, max_slope_ind, k)
                        array.remove(sup_lines, max_slope_ind + 1)
                        array.remove(sup_slopes, max_slope_ind + 1)


if not show_lines
    len_l = array.size(sup_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(sup_lines)
            array.pop(sup_slopes)
            line.delete(to_delete)


// EMA Line
// Script settings
emaLength1 = input.int(title="EMA Length 1", defval=5, minval=2, group = g_ema)
emaSource1 = input.source(title="EMA Source 1", defval=close, group = g_ema)
emaLength2 = input.int(title="EMA Length 2", defval=10, minval=2, group = g_ema)
emaSource2 = input.source(title="EMA Source 2", defval=close, group = g_ema)
emaLength3 = input.int(title="EMA Length 3", defval=20, minval=2, group = g_ema)
emaSource3 = input.source(title="EMA Source 3", defval=close, group = g_ema)
emaLength4 = input.int(title="EMA Length 4", defval=50, minval=2, group = g_ema)
emaSource4 = input.source(title="EMA Source 4", defval=close, group = g_ema)
emaLength5 = input.int(title="EMA Length 5", defval=200, minval=2, group = g_ema)
emaSource5 = input.source(title="EMA Source 5", defval=close, group = g_ema)


// Get EMA values for different lengths
ema1 = ta.ema(emaSource1, emaLength1)
ema2 = ta.ema(emaSource2, emaLength2)
ema3 = ta.ema(emaSource3, emaLength3)
ema4 = ta.ema(emaSource4, emaLength4)
ema5 = ta.ema(emaSource5, emaLength5)

// Plot EMAs
plot(showEMA1 ? ema1 : na, color=close[1] > ema1 and close > ema1 ? color.blue : color.red, linewidth=2, title="EMA 1")
plot(showEMA2 ? ema2 : na, color=close[1] > ema2 and close > ema2 ? color.aqua : color.red, linewidth=2, title="EMA 2")
plot(showEMA3 ? ema3 : na, color=close[1] > ema3 and close > ema3 ? color.green : color.red, linewidth=2, title="EMA 3")
plot(showEMA4 ? ema4 : na, color=close[1] > ema4 and close > ema4 ? color.yellow : color.red, linewidth=2, title="EMA 4")
plot(showEMA5 ? ema5 : na, color=close[1] > ema5 and close > ema5 ? color.orange : color.red, linewidth=2, title="EMA 5")
