// Auto Fibonacci Retracement
//@version=5
indicator("Auto Fibonacci Retracement + Market Structure + Smooth And Lazy Moving Average + Auto Support and Ressistance", "Teknik Kagebunshin", overlay=true)
g_afr = 'Auto Fibonacci Retracement'
g_ms = 'Market Structure'
g_slm = 'SALMA'
g_sr = 'Auto Support and Ressistance'
show_fib = input(true, "Show Fibonacci Retracement", display = display.data_window)
devTooltip = "Deviation is a multiplier that affects how much the price should deviate from the previous pivot in order for the bar to become a new pivot."
depthTooltip = "The minimum number of bars that will be taken into account when calculating the indicator."
// pivots threshold
threshold_multiplier = input.float(title="Deviation", defval=3, minval=0, tooltip=devTooltip, group = g_afr)
depth = input.int(title="Depth", defval=10, minval=2, tooltip=depthTooltip, group = g_afr)
reverse = input(false, "Reverse", display = display.data_window, group = g_afr)
var extendLeft = input(false, "Extend Left    |    Extend Right", inline = "Extend Lines", group = g_afr)
var extendRight = input(true, "", inline = "Extend Lines", group = g_afr)
var extending = extend.none
if extendLeft and extendRight
    extending := extend.both
if extendLeft and not extendRight
    extending := extend.left
if not extendLeft and extendRight
    extending := extend.right
prices = input(true, "Show Prices", display = display.data_window)
levels = input(true, "Show Levels", inline = "Levels", display = display.data_window)
levelsFormat = input.string("Values", "", options = ["Values", "Percent"], inline = "Levels", display = display.data_window)
labelsPosition = input.string("Left", "Labels Position", options = ["Left", "Right"], display = display.data_window)
var int backgroundTransparency = input.int(85, "Background Transparency", minval = 0, maxval = 100, display = display.data_window)

import TradingView/ZigZag/7 as zigzag

update()=>
    var settings = zigzag.Settings.new(threshold_multiplier, depth, color(na), false, false, false, false, "Absolute", true)
    var zigzag.ZigZag zigZag = zigzag.newInstance(settings)
    var zigzag.Pivot lastP = na
    var float startPrice = na
    var float height = na
    if barstate.islast and zigZag.pivots.size() < 2
        runtime.error("Not enough data to calculate Auto Fib Retracement on the current symbol. Change the chart's timeframe to a lower one or select a smaller calculation depth using the indicator's `Depth` settings.")
    settings.devThreshold := ta.atr(10) / close * 100 * threshold_multiplier
    if zigZag.update()
        lastP := zigZag.lastPivot()
        if not na(lastP)
            var line lineLast = na
            if na(lineLast)
                lineLast := line.new(lastP.start, lastP.end, xloc=xloc.bar_time, color=color.gray, width=1, style=line.style_dashed)
            else
                line.set_first_point(lineLast, lastP.start)
                line.set_second_point(lineLast, lastP.end)

            startPrice := reverse ? lastP.start.price : lastP.end.price
            endPrice = reverse ? lastP.end.price : lastP.start.price
            height := (startPrice > endPrice ? -1 : 1) * math.abs(startPrice - endPrice)
    [lastP, startPrice, height]

[lastP, startPrice, height] = update()

_draw_line(price, col) =>
    var id = line.new(lastP.start.time, lastP.start.price, time, price, xloc=xloc.bar_time, color=col, width=1, extend=extending)
    line.set_xy1(id, lastP.start.time, price)
    line.set_xy2(id, lastP.end.time, price)
	id


_draw_label(price, txt, txtColor) =>
    x = labelsPosition == "Left" ? lastP.start.time : not extendRight ? lastP.end.time : time
    labelStyle = labelsPosition == "Left" ? label.style_label_right : label.style_label_left
    align = labelsPosition == "Left" ? text.align_right : text.align_left
    labelsAlignStrLeft = txt + '\n ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏ \n'
    labelsAlignStrRight = '       ' + txt + '\n ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏ \n'
    labelsAlignStr = labelsPosition == "Left" ? labelsAlignStrLeft : labelsAlignStrRight
    var id = label.new(x=x, y=price, xloc=xloc.bar_time, text=labelsAlignStr, textcolor=txtColor, style=labelStyle, textalign=align, color=#00000000)
    label.set_xy(id, x, price)
    label.set_text(id, labelsAlignStr)
    label.set_textcolor(id, txtColor)

_wrap(txt) =>
    "(" + str.tostring(txt, format.mintick) + ")"

_label_txt(level, price) =>
    l = levelsFormat == "Values" ? str.tostring(level) : str.tostring(level * 100) + "%"
    (levels ? l : "") + (prices ? _wrap(price) : "")

_crossing_level(series float sr, series float r) =>
    (r > sr and r < sr[1]) or (r < sr and r > sr[1])

processLevel(bool show, float value, color colorL, line lineIdOther) =>
    if show and show_fib and not na(lastP)
        float m = value
        r = startPrice + height * m
        crossed = _crossing_level(close, r)
        lineId = _draw_line(r, colorL)
        _draw_label(r, _label_txt(m, r), colorL)
        if crossed
            alert("Autofib: " + syminfo.ticker + " crossing level " + str.tostring(value))
        if not na(lineIdOther)
            linefill.new(lineId, lineIdOther, color = color.new(colorL, backgroundTransparency))
        lineId
    else
        lineIdOther


show_0 = input(true, "", inline = "Level0", display = display.data_window)
value_0 = input(0, "", inline = "Level0", display = display.data_window)
color_0 = input(#787b86, "", inline = "Level0", display = display.data_window)

show_0_236 = input(true, "", inline = "Level0", display = display.data_window)
value_0_236 = input(0.236, "", inline = "Level0", display = display.data_window)
color_0_236 = input(#f44336, "", inline = "Level0", display = display.data_window)

show_0_382 = input(true, "", inline = "Level1", display = display.data_window)
value_0_382 = input(0.382, "", inline = "Level1", display = display.data_window)
color_0_382 = input(#81c784, "", inline = "Level1", display = display.data_window)

show_0_5 = input(true, "", inline = "Level1", display = display.data_window)
value_0_5 = input(0.5, "", inline = "Level1", display = display.data_window)
color_0_5 = input(#4caf50, "", inline = "Level1", display = display.data_window)

show_0_618 = input(true, "", inline = "Level2", display = display.data_window)
value_0_618 = input(0.618, "", inline = "Level2", display = display.data_window)
color_0_618 = input(#009688, "", inline = "Level2", display = display.data_window)

show_0_65 = input(false, "", inline = "Level2", display = display.data_window)
value_0_65 = input(0.65, "", inline = "Level2", display = display.data_window)
color_0_65 = input(#009688, "", inline = "Level2", display = display.data_window)

show_0_786 = input(true, "", inline = "Level3", display = display.data_window)
value_0_786 = input(0.786, "", inline = "Level3", display = display.data_window)
color_0_786 = input(#64b5f6, "", inline = "Level3", display = display.data_window)

show_1 = input(true, "", inline = "Level3", display = display.data_window)
value_1 = input(1, "", inline = "Level3", display = display.data_window)
color_1 = input(#787b86, "", inline = "Level3", display = display.data_window)

show_1_272 = input(false, "", inline = "Level4", display = display.data_window)
value_1_272 = input(1.272, "", inline = "Level4", display = display.data_window)
color_1_272 = input(#81c784, "", inline = "Level4", display = display.data_window)

show_1_414 = input(false, "", inline = "Level4", display = display.data_window)
value_1_414 = input(1.414, "", inline = "Level4", display = display.data_window)
color_1_414 = input(#f44336, "", inline = "Level4", display = display.data_window)

show_1_618 = input(true, "", inline = "Level5", display = display.data_window)
value_1_618 = input(1.618, "", inline = "Level5", display = display.data_window)
color_1_618 = input(#2962ff, "", inline = "Level5", display = display.data_window)

show_1_65 = input(false, "", inline = "Level5", display = display.data_window)
value_1_65 = input(1.65, "", inline = "Level5", display = display.data_window)
color_1_65 = input(#2962ff, "", inline = "Level5", display = display.data_window)

show_2_618 = input(true, "", inline = "Level6", display = display.data_window)
value_2_618 = input(2.618, "", inline = "Level6", display = display.data_window)
color_2_618 = input(#f44336, "", inline = "Level6", display = display.data_window)

show_2_65 = input(false, "", inline = "Level6", display = display.data_window)
value_2_65 = input(2.65, "", inline = "Level6", display = display.data_window)
color_2_65 = input(#f44336, "", inline = "Level6", display = display.data_window)

show_3_618 = input(true, "", inline = "Level7", display = display.data_window)
value_3_618 = input(3.618, "", inline = "Level7", display = display.data_window)
color_3_618 = input(#9c27b0, "", inline = "Level7", display = display.data_window)

show_3_65 = input(false, "", inline = "Level7", display = display.data_window)
value_3_65 = input(3.65, "", inline = "Level7", display = display.data_window)
color_3_65 = input(#9c27b0, "", inline = "Level7", display = display.data_window)

show_4_236 = input(true, "", inline = "Level8", display = display.data_window)
value_4_236 = input(4.236, "", inline = "Level8", display = display.data_window)
color_4_236 = input(#e91e63, "", inline = "Level8", display = display.data_window)

show_4_618 = input(false, "", inline = "Level8", display = display.data_window)
value_4_618 = input(4.618, "", inline = "Level8", display = display.data_window)
color_4_618 = input(#81c784, "", inline = "Level8", display = display.data_window)

show_neg_0_236 = input(false, "", inline = "Level9", display = display.data_window)
value_neg_0_236 = input(-0.236, "", inline = "Level9", display = display.data_window)
color_neg_0_236 = input(#f44336, "", inline = "Level9", display = display.data_window)

show_neg_0_382 = input(false, "", inline = "Level9", display = display.data_window)
value_neg_0_382 = input(-0.382, "", inline = "Level9", display = display.data_window)
color_neg_0_382 = input(#81c784, "", inline = "Level9", display = display.data_window)

show_neg_0_618 = input(false, "", inline = "Level10", display = display.data_window)
value_neg_0_618 = input(-0.618, "", inline = "Level10", display = display.data_window)
color_neg_0_618 = input(#009688, "", inline = "Level10", display = display.data_window)

show_neg_0_65 = input(false, "", inline = "Level10", display = display.data_window)
value_neg_0_65 = input(-0.65, "", inline = "Level10", display = display.data_window)
color_neg_0_65 = input(#009688, "", inline = "Level10", display = display.data_window)


if show_fib
    lineId0 = processLevel(show_neg_0_65, value_neg_0_65, color_neg_0_65, line(na))
    lineId1 = processLevel(show_neg_0_618, value_neg_0_618, color_neg_0_618, lineId0)
    lineId2 = processLevel(show_neg_0_382, value_neg_0_382, color_neg_0_382, lineId1)
    lineId3 = processLevel(show_neg_0_236, value_neg_0_236, color_neg_0_236, lineId2)
    lineId4 = processLevel(show_0, value_0, color_0, lineId3)
    lineId5 = processLevel(show_0_236, value_0_236, color_0_236, lineId4)
    lineId6 = processLevel(show_0_382, value_0_382, color_0_382, lineId5)
    lineId7 = processLevel(show_0_5, value_0_5, color_0_5, lineId6)
    lineId8 = processLevel(show_0_618, value_0_618, color_0_618, lineId7)
    lineId9 = processLevel(show_0_65, value_0_65, color_0_65, lineId8)
    lineId10 = processLevel(show_0_786, value_0_786, color_0_786, lineId9)
    lineId11 = processLevel(show_1, value_1, color_1, lineId10)
    lineId12 = processLevel(show_1_272, value_1_272, color_1_272, lineId11)
    lineId13 = processLevel(show_1_414, value_1_414, color_1_414, lineId12)
    lineId14 = processLevel(show_1_618, value_1_618, color_1_618, lineId13)
    lineId15 = processLevel(show_1_65, value_1_65, color_1_65, lineId14)
    lineId16 = processLevel(show_2_618, value_2_618, color_2_618, lineId15)
    lineId17 = processLevel(show_2_65, value_2_65, color_2_65, lineId16)
    lineId18 = processLevel(show_3_618, value_3_618, color_3_618, lineId17)
    lineId19 = processLevel(show_3_65, value_3_65, color_3_65, lineId18)
    lineId20 = processLevel(show_4_236, value_4_236, color_4_236, lineId19)
    lineId21 = processLevel(show_4_618, value_4_618, color_4_618, lineId20)


//Market Structure - By Leviathan
// Constants
color CLEAR = color.rgb(0,0,0,100)

// Inputs
swingSize = input.int(20, 'Swing Length', tooltip='The number of left and right bars checked when searching for a swing point. Higher value = less swing points plotted and lower value = more swing points plotted.', group = g_ms )
bosConfType = input.string('Candle Close', 'BOS Confirmation', ['Candle Close', 'Wicks'], tooltip='Choose whether candle close/wick above previous swing point counts as a BOS.', group = g_ms)
choch = input.bool(true, 'Show CHoCH', tooltip='Renames the first counter trend BOS to CHoCH', group = g_ms )
showSwing = input.bool(true, 'Show Swing Points', tooltip='Show or hide HH, LH, HL, LL', group = g_ms)

showHalf = input.bool(false, 'Show 0.5 Retracement Level', group='0.5 Retracement Level', tooltip='Show a possible 0.5 retracement level between the swing highs and lows of an expansion move.')
halfColor = input.color(color.rgb(41, 39, 176), 'Color', group='0.5 Retracement Level')
halfStyle = input.string('Solid', 'Line Style', ['Solid', 'Dashed', 'Dotted'], group='0.5 Retracement Level')
halfWidth = input.int(1, 'Width', minval=1, group='0.5 Retracement Level')

bosColor = input.color(color.rgb(112, 114, 119), 'Color', group='BOS Settings')
bosStyle = input.string('Dashed', 'Line Style', ['Solid', 'Dashed', 'Dotted'], group='BOS Settings')
bosWidth = input.int(1, 'Width', minval=1, group='BOS Settings')

// Functions
lineStyle(x) =>
    switch x
        'Solid' => line.style_solid
        'Dashed' => line.style_dashed
        'Dotted' => line.style_dotted


// Calculations

//Finding high and low pivots
pivHi = ta.pivothigh(high, swingSize, swingSize)
pivLo = ta.pivotlow(low, swingSize, swingSize)


//Tracking the previous swing levels to determine hh lh hl ll
var float prevHigh = na
var float prevLow = na
var int prevHighIndex = na
var int prevLowIndex = na

//Tracking whether previous levels have been breached
var bool highActive = false
var bool lowActive = false

bool hh = false
bool lh = false
bool hl = false
bool ll = false

//Variable to track the previous swing type, used later on to draw 0.5 Retracement Levels (HH = 2, LH = 1, HL = -1, LL = -2)
var int prevSwing = 0

if not na(pivHi)
    if pivHi >= prevHigh
        hh := true
        prevSwing := 2
    else
        lh := true
        prevSwing := 1
    prevHigh := pivHi
    highActive := true
    prevHighIndex := bar_index - swingSize

if not na(pivLo)
    if pivLo >= prevLow
        hl := true
        prevSwing := -1
    else
        ll := true
        prevSwing := -2
    prevLow := pivLo
    lowActive := true
    prevLowIndex := bar_index - swingSize

//Generating the breakout signals
bool highBroken = false
bool lowBroken = false

//Tracking prev breakout
var int prevBreakoutDir = 0

float highSrc = bosConfType == 'Candle Close' ? close : high
float lowSrc = bosConfType == 'Candle Close' ? close : low

if highSrc > prevHigh and highActive
    highBroken := true
    highActive := false
if lowSrc < prevLow and lowActive
    lowBroken := true
    lowActive := false


// Visual Output

//Swing level labels
if hh and showSwing
    label.new(bar_index - swingSize, pivHi, 'HH', color=CLEAR, style=label.style_label_down, textcolor=chart.fg_color)
    //Detecting if it is a hh after a hl
    if prevSwing[1] == -1 and showHalf
        line.new(prevLowIndex, (prevLow + pivHi) / 2, bar_index - swingSize, (prevLow + pivHi) / 2, color=halfColor, style=lineStyle(halfStyle), width=halfWidth)
if lh and showSwing
    label.new(bar_index - swingSize, pivHi, 'LH', color=CLEAR, style=label.style_label_down, textcolor=chart.fg_color)
if hl and showSwing
    label.new(bar_index - swingSize, pivLo, 'HL', color=CLEAR, style=label.style_label_up, textcolor=chart.fg_color)
if ll and showSwing
    label.new(bar_index - swingSize, pivLo, 'LL', color=CLEAR, style=label.style_label_up, textcolor=chart.fg_color)
    //Detecting if it is a ll after a lh
    if prevSwing[1] == 1 and showHalf
        line.new(prevHighIndex, (prevHigh + pivLo) / 2, bar_index - swingSize, (prevHigh + pivLo) / 2, color=halfColor, style=lineStyle(halfStyle), width=halfWidth)

//Generating the BOS Lines
if highBroken
    line.new(prevHighIndex, prevHigh, bar_index, prevHigh, color=bosColor, style=lineStyle(bosStyle), width=bosWidth)
    label.new(math.floor(bar_index - (bar_index - prevHighIndex) / 2), prevHigh, prevBreakoutDir == -1 and choch ? 'CHoCH' : 'BOS', color=CLEAR, textcolor=bosColor, size=size.tiny)
    prevBreakoutDir := 1
if lowBroken
    line.new(prevLowIndex, prevLow, bar_index, prevLow, color=bosColor, style=lineStyle(bosStyle), width=bosWidth)
    label.new(math.floor(bar_index - (bar_index - prevLowIndex) / 2), prevLow, prevBreakoutDir == 1 and choch ? 'CHoCH' : 'BOS', color=CLEAR, textcolor=bosColor, style=label.style_label_up, size=size.tiny)
    prevBreakoutDir := -1

// SALMA

// Corrects price points within specific StdDev band before calculating a smoothed WMA

price       = input(close, 'Source', group = g_slm)
length      = input.int(10, 'Length', minval=1, group = g_slm)
smooth      = input.int(3, 'Extra Smooth [1 = None]', minval=1, group = g_slm)

mult        = input.float(0.3, minval=0.05, maxval=3, step=0.05,
  title='Width', inline = 'SD Channel', group='Volatility Filter (SD Channel)')

sd_len      = input.int(5, minval=1,
  title='Length', inline = 'SD Channel', group='Volatility Filter (SD Channel)')

baseline    = ta.wma(price, sd_len)
dev         = mult * ta.stdev(price, sd_len)
upper       = baseline + dev
lower       = baseline - dev

cprice      = price > upper ? upper : price < lower ? lower : price

// Uncomment these code lines to expose the base StdDev channel used as volatility filter
//plot (baseline, "Base MA")
//plot(upper, "Upper Band", color=color.green)
//plot(lower, "Lower Band", color=color.red)

REMA        = ta.wma(ta.wma(cprice, length), smooth)

c_up        = color.new(#33ff00, 0)
c_dn        = color.new(#ff1111, 0)

REMA_up     = REMA > REMA[1]

// Add show or hide option for the SALMA line
show_salma  = input.bool(true, title="Show SALMA Line", group="SALMA Settings")
plot(show_salma ? REMA : na, title='SALMA', color=REMA_up ? c_up : c_dn, linewidth=3)

// ======================================================================================================
// Add optional MA's - to enable us to track what many other traders are working with
// These MA's will be hidden by default until user exposes them as needed in the Settings
// The below code is based on the built-in MA Ribbon in the TV library - with some modifications

// ======================================================================
f_ma(source, length, mtype) =>
    mtype    == 'SMA' ? ta.sma(source, length) :
      mtype  == 'EMA' ? ta.ema(source, length) :
      ta.wma(source, length)
// ======================================================================
gr_ma       = 'Optional MA\'s'
t_ma1       = 'MA #1'
t_ma2       = 'MA #2'
t_ma3       = 'MA #3'

// MA #1
show_ma1    = input.bool(false, t_ma1, inline=t_ma1, group=gr_ma)
ma1_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma1, group=gr_ma)
ma1_source  = input.source(close, '', inline=t_ma1, group=gr_ma)
ma1_length  = input.int(50, '', minval=1, inline=t_ma1, group=gr_ma)
ma1_color   = #59ff00
ma1         = f_ma(ma1_source, ma1_length, ma1_type)
plot(show_ma1 ? ma1 : na, color = color.new(ma1_color, 0), title = t_ma1, linewidth = 1)

// MA #2
show_ma2    = input.bool(false, t_ma2, inline=t_ma2, group=gr_ma)
ma2_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma2, group=gr_ma)
ma2_source  = input.source(close, '', inline=t_ma2, group=gr_ma)
ma2_length  = input.int(100, '', minval=1, inline=t_ma2, group=gr_ma)
ma2_color   = #ff7b5a
ma2         = f_ma(ma2_source, ma2_length, ma2_type)
plot(show_ma2 ? ma2 : na, color = color.new(ma2_color, 0), title = t_ma2, linewidth = 1)

// MA #3
show_ma3    = input.bool(false, t_ma3, inline=t_ma3, group=gr_ma)
ma3_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma3, group=gr_ma)
ma3_source  = input.source(close, '', inline=t_ma3, group=gr_ma)
ma3_length  = input.int(200, '', minval=1, inline=t_ma3, group=gr_ma)
ma3_color   = #ffc100
ma3         = f_ma(ma3_source, ma3_length, ma3_type)
plot(show_ma3 ? ma3 : na, color = color.new(ma3_color, 0), title = t_ma3, linewidth = 1)

// ================================================================================================================
// v3: Adding alerts for swing up/down and any swing
// ================================================================================================================

SwingDn     = REMA_up[1] and not(REMA_up)
SwingUp     = REMA_up    and not(REMA_up[1])

alertcondition(SwingUp, ". SALMA Swing Up", "SALMA Swing Up Detected!")                   // explicit swing up
alertcondition(SwingDn, ".. SALMA Swing Down", "SALMA Swing Down Detected!")              // explicit swing down
alertcondition(SwingUp or SwingDn, "... SALMA Swing", "SALMA Swing Detected!")            // Detect any swing

// Auto Support and Resistance

// Input to hide or show the indicator
showIndicator = input.bool(true, title="Show Indicator Auto Support and Resistance", group = g_sr)

// Input for Bars Back
barsBack = input.int(100, minval=2, step=1, title="(x) Bars Back", group = g_sr)

var line tl = na // Trendline
var line support = na // Support line
var line resistance = na // Resistance line

// Function to get Resistance
findResistance(barsBack) =>
    ta.highest(close, barsBack)

// Function to get Support
findSupport(barsBack) =>
    ta.lowest(close, barsBack)

r = findResistance(barsBack)
s = findSupport(barsBack)

if barstate.islast and showIndicator
    // Delete old lines
    line.delete(tl)
    line.delete(support)
    line.delete(resistance)

    // Draw new trendline
    tl := line.new(x1 = time[barsBack], y1 = close[barsBack],
                   x2 = time, y2 = close,
                   xloc = xloc.bar_time,
                   color = color.blue,
                   width = 2)

    // Draw resistance line
    resistance := line.new(x1 = time[barsBack], y1=r,
                           x2 = time, y2=r,
                           xloc = xloc.bar_time,
                           color = color.red,
                           width = 1)

    // Draw support line
    support := line.new(x1=time[barsBack], y1=s,
                  x2=time, y2=s,
                  xloc=xloc.bar_time,
                  color=color.green,
                  width=1)
