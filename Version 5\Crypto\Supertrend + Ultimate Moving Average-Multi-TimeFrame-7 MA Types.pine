//@version=5
indicator('Supertrend + Ultimate Moving Average-Multi-TimeFrame-7 MA Types', overlay=true, format=format.price, precision=2, timeframe='')

Periods = input(title='ATR Period', defval=10)
srcST = input(hl2, title='Source')
Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0)
changeATR = input(title='Change ATR Calculation Method ?', defval=true)
showsignals = input(title='Show Buy/Sell Signals ?', defval=true)
highlighting = input(title='Highlighter On/Off ?', defval=true)
atr2 = ta.sma(ta.tr, Periods)
atr = changeATR ? ta.atr(Periods) : atr2
up = srcST - Multiplier * atr
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = srcST + Multiplier * atr
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
upPlot = plot(trend == 1 ? up : na, title='Up Trend', style=plot.style_linebr, linewidth=2, color=color.new(color.green, 0))
buySignal = trend == 1 and trend[1] == -1
plotshape(buySignal ? up : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.green, 0))
plotshape(buySignal and showsignals ? up : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=color.new(color.green, 0), textcolor=color.new(color.white, 0))
dnPlot = plot(trend == 1 ? na : dn, title='Down Trend', style=plot.style_linebr, linewidth=2, color=color.new(color.red, 0))
sellSignal = trend == -1 and trend[1] == 1
plotshape(sellSignal ? dn : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.red, 0))
plotshape(sellSignal and showsignals ? dn : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=color.new(color.red, 0), textcolor=color.new(color.white, 0))
mPlot = plot(ohlc4, title='', style=plot.style_circles, linewidth=0)
longFillColor = highlighting ? trend == 1 ? color.green : color.white : color.white
shortFillColor = highlighting ? trend == -1 ? color.red : color.white : color.white
fill(mPlot, upPlot, title='UpTrend Highligter', color=longFillColor, transp=90)
fill(mPlot, dnPlot, title='DownTrend Highligter', color=shortFillColor, transp=90)
alertcondition(buySignal, title='SuperTrend Buy', message='SuperTrend Buy!')
alertcondition(sellSignal, title='SuperTrend Sell', message='SuperTrend Sell!')
changeCond = trend != trend[1]
alertcondition(changeCond, title='SuperTrend Direction Change', message='SuperTrend has changed direction!')

// Ultimate Moving Average-Multi-TimeFrame-7 MA Types
//inputs
src = close
useCurrentRes = input(true, title='Use Current Chart Resolution?')
resCustom = input.timeframe(title='Use Different Timeframe? Uncheck Box Above', defval='D')
len = input(20, title='Moving Average Length - LookBack Period')
atype = input.int(1, minval=1, maxval=7, title='1=SMA, 2=EMA, 3=WMA, 4=HullMA, 5=VWMA, 6=RMA, 7=TEMA')
cc = input(true, title='Change Color Based On Direction?')
smoothe = input.int(2, minval=1, maxval=10, title='Color Smoothing - 1 = No Smoothing')
doma2 = input(false, title='Optional 2nd Moving Average')
len2 = input(50, title='Moving Average Length - Optional 2nd MA')
atype2 = input.int(1, minval=1, maxval=7, title='1=SMA, 2=EMA, 3=WMA, 4=HullMA, 5=VWMA, 6=RMA, 7=TEMA')
cc2 = input(false, title='Change Color Based On Direction 2nd MA?')
warn = input(false, title='***You Can Turn On The Show Dots Parameter Below Without Plotting 2nd MA to See Crosses***')
warn2 = input(false, title='***If Using Cross Feature W/O Plotting 2ndMA - Make Sure 2ndMA Parameters are Set Correctly***')
sd = input(false, title='Show Dots on Cross of Both MA\'s')


res = useCurrentRes ? timeframe.period : resCustom
//hull ma definition
hullma = ta.wma(2 * ta.wma(src, len / 2) - ta.wma(src, len), math.round(math.sqrt(len)))
//TEMA definition
ema1 = ta.ema(src, len)
ema2 = ta.ema(ema1, len)
ema3 = ta.ema(ema2, len)
tema = 3 * (ema1 - ema2) + ema3

sma_1 = ta.sma(src, len)
ema_1 = ta.ema(src, len)
wma_1 = ta.wma(src, len)
vwma_1 = ta.vwma(src, len)
rma_1 = ta.rma(src, len)
avg = atype == 1 ? sma_1 : atype == 2 ? ema_1 : atype == 3 ? wma_1 : atype == 4 ? hullma : atype == 5 ? vwma_1 : atype == 6 ? rma_1 : tema
//2nd Ma - hull ma definition
hullma2 = ta.wma(2 * ta.wma(src, len2 / 2) - ta.wma(src, len2), math.round(math.sqrt(len2)))
//2nd MA TEMA definition
sema1 = ta.ema(src, len2)
sema2 = ta.ema(sema1, len2)
sema3 = ta.ema(sema2, len2)
stema = 3 * (sema1 - sema2) + sema3

sma_2 = ta.sma(src, len2)
ema_2 = ta.ema(src, len2)
wma_2 = ta.wma(src, len2)
vwma_2 = ta.vwma(src, len2)
rma_2 = ta.rma(src, len2)
avg2 = atype2 == 1 ? sma_2 : atype2 == 2 ? ema_2 : atype2 == 3 ? wma_2 : atype2 == 4 ? hullma2 : atype2 == 5 ? vwma_2 : atype2 == 6 ? rma_2 : tema

out = avg
out_two = avg2

out1 = request.security(syminfo.tickerid, res, out)
out2 = request.security(syminfo.tickerid, res, out_two)

ma_up = out1 >= out1[smoothe]
ma_down = out1 < out1[smoothe]

col = cc ? ma_up ? color.lime : ma_down ? color.red : color.aqua : color.aqua
col2 = cc2 ? ma_up ? color.lime : ma_down ? color.red : color.orange : color.orange

circleYPosition = out2

plot(out1, title='Multi-Timeframe Moving Avg', style=plot.style_line, linewidth=4, color=col)
plot(doma2 and out2 ? out2 : na, title='2nd Multi-TimeFrame Moving Average', style=plot.style_line, linewidth=4, color=col2)
plot(sd and ta.cross(out1, out2) ? circleYPosition : na, style=plot.style_cross, linewidth=5, color=color.new(color.yellow, 0))


