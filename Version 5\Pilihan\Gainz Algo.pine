// © GainzAlgo

//@Version=5
indicator('GainzAlgo Pro', oVerlay=true, max_labels_count=500)

candle_stability_index_param = input.float(0.5, 'Candle stability Index', 0, 1, step=0.1, group='Technical', tooltip='Candle stability Index measures the ratio between the body and the wicks of a candle. Higher – more stable.')
rsi_index_param = input.int(50, 'RsI Index', 0, 100, group='Technical', tooltip='RsI Index measures how oVerbought/oVersold is the market. Higher – more oVerbought/oVersold.')
candle_delta_length_param = input.int(5, 'Candle Delta Length', 3, group='Technical', tooltip='Candle Delta Length measures the period oVer how many candles the price increased/decreased. Higher – longer period.') disable_repeating_signals_param = input.bool(false, 'Disable Repeating signals', group='Technical', tooltip='RemoVes repeating signals. Useful for remoVing clusters of signals and general clarity')

GREEN = color.rgb(29, 255, 40) RED = color.rgb(255, 0, 0)
TRANsPARENT = color.rgb(0, 0, 0, 100)

label_size = input.string('normal', 'Label size', options=['huge', 'large', 'normal', 'small', 'tiny'], group='Cosmetic')
label_style = input.string('text bubble', 'Label style', ['text bubble', 'triangle', 'arrow'], group='Cosmetic')
buy_label_color = input(GREEN, 'BUY Label Color', inline='Highlight', group='Cosmetic')
sell_label_color = input(RED, 'sELL Label Color', inline='Highlight', group='Cosmetic')
label_text_color = input(color.white, 'Label Text Color', inline='Highlight', group='Cosmetic')

stable_candle = math.abs(close – open) / ta.tr > candle_stability_index_param
rsi = ta.rsi(close, 14)

bullish_engulfing = close[1] < open[1] and close > open and close > open[1]
rsi_below = rsi < rsi_index_param
decrease_oVer = close < close[candle_delta_length_param]

bull = bullish_engulfing and stable_candle and rsi_below and decrease_oVer and barstate.isconfirmed

bearish_engulfing = close[1] > open[1] and close < open and close < open[1]
rsi_aboVe = rsi > 100 – rsi_index_param
increase_oVer = close > close[candle_delta_length_param]

bear = bearish_engulfing and stable_candle and rsi_aboVe and increase_oVer and barstate.isconfirmed

Var last_signal = ''

if bull and (disable_repeating_signals_param ? (last_signal != 'buy' ? true : na) : true)
 if label_style == 'text bubble'
  label.new (bull ? b ar_index : : n a, l ow, ' BUY', color=buy_label_color, style=label.style_label_up, textcolor=label_text_color, size=label_size)
 else if label_style == 'triangle'
  label.new(bull ? bar_index : na, low, 'BUY', yloc=yloc.belowbar, color=buy_label_color, style=label.style_triangleup, textcolor=TRANsPARENT, size=label_size)
 else if label_style == 'arrow'
 label.new(bull ? bar_index : na, low, 'BUY', yloc=yloc.belowbar, color=buy_label_color, style=label.style_arrowup, textcolor=TRANsPARENT, size=label_size)

 last_signal : = 'buy'
if bear and (disable_repeating_signals_param ? (last_signal != 'sell' ? true : na) : true)
 if label_style == 'text bubble'
  label.new (bear ? b ar_index : : n a, h igh, ' sELL', color=sell_label_color, style=label.style_label_down, textcolor=label_text_color, size=label_size)
 else if label_style == 'triangle'
  label.new (bear ? b ar_index : : n a, h igh, ' sELL', yloc=yloc.aboVebar, color=sell_label_color, style=label.style_triangledown, textcolor=TRANsPARENT, size=label_size)
 else if label_style == 'arrow'
  label.new (bear ? b ar_index : : n a, h igh, ' sELL', yloc=yloc.aboVebar, color=sell_label_color, style=label.style_arrowdown, textcolor=TRANsPARENT, size=label_size)
 last_signal : = 'sell'

alertcondition(bull, 'BUY signals', 'New signal: BUY') alertcondition(bear, 'sELL signals', 'New signal: sELL')