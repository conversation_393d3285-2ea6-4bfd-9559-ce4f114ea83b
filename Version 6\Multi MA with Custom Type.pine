//@version=6
indicator('Multi MA with Custom Type', overlay = true)

f_ma(source, length, mtype) =>
    mtype == 'SMA' ? ta.sma(source, length) : mtype == 'EMA' ? ta.ema(source, length) : ta.wma(source, length)

gr_ma = 'Optional MA\'s'
t_ma1 = 'MA #1'
t_ma2 = 'MA #2'
t_ma3 = 'MA #3'

// Inputs for MA1
show_ma1 = input.bool(true, t_ma1, inline = t_ma1, group = gr_ma)
ma1_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma1, group = gr_ma)
ma1_source = input.source(close, '', inline = t_ma1, group = gr_ma)
ma1_length = input.int(20, '', minval = 1, inline = t_ma1, group = gr_ma)
ma1_color = #59ff00
ma1 = f_ma(ma1_source, ma1_length, ma1_type)

// Inputs for MA2
show_ma2 = input.bool(true, t_ma2, inline = t_ma2, group = gr_ma)
ma2_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma2, group = gr_ma)
ma2_source = input.source(close, '', inline = t_ma2, group = gr_ma)
ma2_length = input.int(50, '', minval = 1, inline = t_ma2, group = gr_ma)
ma2_color = #ff7b5a
ma2 = f_ma(ma2_source, ma2_length, ma2_type)

// Inputs for MA3
show_ma3 = input.bool(true, t_ma3, inline = t_ma3, group = gr_ma)
ma3_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma3, group = gr_ma)
ma3_source = input.source(close, '', inline = t_ma3, group = gr_ma)
ma3_length = input.int(100, '', minval = 1, inline = t_ma3, group = gr_ma)
ma3_color = #ffc100
ma3 = f_ma(ma3_source, ma3_length, ma3_type)

// Plot and fill logic for MA1
plot0_ma1 = plot(ma1_source, display = display.none, editable = false)
css_ma1 = ma1_source > ma1 ? #59ff00 : #ff1100
plot1_ma1 = plot(show_ma1 ? ma1 : na, title = t_ma1, color = css_ma1, linewidth = 1, display = show_ma1 ? display.all : display.none)
fill_css_ma1 = ma1_source > ma1 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma1, plot1_ma1, color = fill_css_ma1, title = 'Fill MA1')

// Plot and fill logic for MA2
plot0_ma2 = plot(ma2_source, display = display.none, editable = false)
css_ma2 = ma2_source > ma2 ? #ff7b5a : #ff1100
plot1_ma2 = plot(show_ma2 ? ma2 : na, title = t_ma2, color = css_ma2, linewidth = 1, display = show_ma2 ? display.all : display.none)
fill_css_ma2 = ma2_source > ma2 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma2, plot1_ma2, color = fill_css_ma2, title = 'Fill MA2')

// Plot and fill logic for MA3
plot0_ma3 = plot(ma3_source, display = display.none, editable = false)
css_ma3 = ma3_source > ma3 ? #ffc100 : #ff1100
plot1_ma3 = plot(show_ma3 ? ma3 : na, title = t_ma3, color = css_ma3, linewidth = 1, display = show_ma3 ? display.all : display.none)
fill_css_ma3 = ma3_source > ma3 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma3, plot1_ma3, color = fill_css_ma3, title = 'Fill MA3')
