//@version=6
indicator(title = 'The RealGains Algo', shorttitle = 'The RealGains Algo 2.5', overlay = true)

// Color variables
upColor = color.white
midColor = #90bff9
downColor = color.blue

// Source
src = input(defval = close, title = 'Source')

// Sampling Period
per = input.int(defval = 100, minval = 1, title = 'Sampling Period')

// Range Multiplier
mult = input.float(defval = 3.0, minval = 0.1, title = 'Range Multiplier')

// SMA Settings
g_sma = "Choose SMAs"
showSMA20 = input.bool(true, title = 'Show 20 SMA', group=g_sma)
showSMA50 = input.bool(true, title = 'Show 50 SMA', group=g_sma)
showSMA200 = input.bool(true, title = 'Show 200 SMA', group=g_sma)

// Signal Filter Settings
g_filter = "Apply SMAs Filter"
useSMA20Filter = input.bool(true, title="Filter Signals using SMA 20", group=g_filter)
useSMA50Filter = input.bool(true, title="Filter Signals using SMA 50", group=g_filter)
useSMA200Filter = input.bool(true, title="Additional Filter using SMA 200", group=g_filter)

// Calculate SMAs
sma20 = ta.sma(src, 20)
sma50 = ta.sma(src, 50)
sma200 = ta.sma(src, 200)

// Dynamic color logic for SMAs
sma20Color = src > sma20 ? color.new(color.green, 0) : color.new(color.red, 0)
sma50Color = src > sma50 ? color.new(color.green, 0) : color.new(color.red, 0)
sma200Color = src > sma200 ? color.new(color.green, 0) : color.new(color.red, 0)

// Plot SMAs with dynamic coloring
plot(showSMA20 ? sma20 : na, color = sma20Color, linewidth = 1, title = 'SMA 20')
plot(showSMA50 ? sma50 : na, color = sma50Color, linewidth = 1, title = 'SMA 50')
plot(showSMA200 ? sma200 : na, color = sma200Color, linewidth = 2, title = 'SMA 200')

// Smooth Average Range
smoothrng(x, t, m) =>
    wper = t * 2 - 1
    avrng = ta.ema(math.abs(x - x[1]), t)
    smoothrng = ta.ema(avrng, wper) * m
    smoothrng
smrng = smoothrng(src, per, mult)

// Range Filter
rngfilt(x, r) =>
    rngfilt = x
    rngfilt := x > nz(rngfilt[1]) ? x - r < nz(rngfilt[1]) ? nz(rngfilt[1]) : x - r : x + r > nz(rngfilt[1]) ? nz(rngfilt[1]) : x + r
    rngfilt
filt = rngfilt(src, smrng)

// Filter Direction
upward = 0.0
upward := filt > filt[1] ? nz(upward[1]) + 1 : filt < filt[1] ? 0 : nz(upward[1])
downward = 0.0
downward := filt < filt[1] ? nz(downward[1]) + 1 : filt > filt[1] ? 0 : nz(downward[1])

// Target Bands
hband = filt + smrng
lband = filt - smrng

// Colors
filtcolor = upward > 0 ? upColor : downward > 0 ? downColor : midColor
barcolor = src > filt and src > src[1] and upward > 0 ? upColor : src > filt and src < src[1] and upward > 0 ? upColor : src < filt and src < src[1] and downward > 0 ? downColor : src < filt and src > src[1] and downward > 0 ? downColor : midColor

filtplot = plot(filt, color = filtcolor, linewidth = 2, title = 'Range Filter')

// Target
hbandplot = plot(hband, color = color.new(upColor, 70), title = 'High Target')
lbandplot = plot(lband, color = color.new(downColor, 70), title = 'Low Target')

// Fills
fill(hbandplot, filtplot, color = color.new(upColor, 90), title = 'High Target Range')
fill(lbandplot, filtplot, color = color.new(downColor, 90), title = 'Low Target Range')

// Bar Color
barcolor(barcolor)

// Break Outs
longCond = bool(na)
shortCond = bool(na)
longCond := src > filt and src > src[1] and upward > 0 or src > filt and src < src[1] and upward > 0
shortCond := src < filt and src < src[1] and downward > 0 or src < filt and src > src[1] and downward > 0

CondIni = 0
CondIni := longCond ? 1 : shortCond ? -1 : CondIni[1]

// Apply SMA filters to original signals
validLongSignal = longCond and CondIni[1] == -1 and
                  (not useSMA20Filter or src > sma20) and     // SMA 20 filter
                  (not useSMA50Filter or src > sma50) and     // SMA 50 filter
                  (not useSMA200Filter or src > sma200)       // SMA 200 filter

validShortSignal = shortCond and CondIni[1] == 1 and
                   (not useSMA20Filter or src < sma20) and    // SMA 20 filter
                   (not useSMA50Filter or src < sma50) and    // SMA 50 filter
                   (not useSMA200Filter or src < sma200)      // SMA 200 filter

// Alerts
plotshape(validLongSignal, title = 'Buy Signal', text = 'Buy+', textcolor = color.white, style = shape.labelup, size = size.small, location = location.belowbar, color = color.new(#aaaaaa, 20))
plotshape(validShortSignal, title = 'Sell Signal', text = 'Sell+', textcolor = color.white, style = shape.labeldown, size = size.small, location = location.abovebar, color = color.new(downColor, 20))

alertcondition(validLongSignal, title = 'Buy alert on Range Filter', message = 'Buy alert on Range Filter')
alertcondition(validShortSignal, title = 'Sell alert on Range Filter', message = 'Sell alert on Range Filter')
alertcondition(validLongSignal or validShortSignal, title = 'Buy and Sell alert on Range Filter', message = 'Buy and Sell alert on Range Filter')




//* TTD BAR SETTINGS *//

paint_bars = input(false, title = 'Paint bars?', group = 'Bars Settings')
catch_flat = input(false, title = 'Try to catch flat?', group = 'Bars Settings')
uptrend_colour = input.color(defval = color.green, title = 'Uptrend colour', group = 'Bars Settings')
downtrend_colour = input.color(defval = color.red, title = 'Downtrend colour', group = 'Bars Settings')
neutraltrend_colour = input.color(defval = color.gray, title = 'Downtrend colour', tooltip = 'Note that this value is ignored if the setting to catch flat is switched off.', group = 'Bars Settings')

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//* TABLE SETTINGS *//

show_header = input(false, title = 'Show header?', group = 'Table Settings')
show_ema_value = input(false, title = 'Show EMA value?', group = 'Table Settings')
dashboard_position = input.session('Middle right', 'Position', ['Top right', 'Bottom right', 'Top left', 'Bottom left', 'Top center', 'Bottom center', 'Middle right'], group = 'Table Settings')
text_size = input.session('Normal', 'Size', options = ['Tiny', 'Small', 'Normal', 'Large'], group = 'Table Settings')
text_color = input(#d1d4dc, title = 'Text colour', group = 'Table Settings')
table_color = input(color.gray, title = 'Border colour', group = 'Table Settings')
uptrend_indicator = input('🟢', title = 'Uptrend indicator', group = 'Table Settings')
downtrend_indicator = input('🔴', title = 'Downtrend indicator', group = 'Table Settings')
neutraltrend_indicator = input('⚫️', title = 'Neutraltrend indicator', tooltip = 'Note that this value is ignored if the setting to catch flat is switched off.', group = 'Table Settings')

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//* EMA SETTINGS *//

trend_identification_approach = input.session('Direction of a single EMA', 'Trend identification approach', ['Direction of a single EMA', 'Comparison of the two EMAs'], group = 'EMA Settings')
ema1_length = input.int(title = 'EMA length', defval = 50, minval = 1, maxval = 800, group = 'EMA Settings')
ema2_length = input.int(title = 'Additional EMA length', defval = 200, minval = 20, maxval = 800, tooltip = 'Note that the single EMA trend identification approach ignores this value.', group = 'EMA Settings')

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//* TIMEFRAME SETTINGS *//

show_1m = input(true, title = 'Show 1m', group = 'Timeframe Settings')
show_2m = input(false, title = 'Show 2m ', group = 'Timeframe Settings')
show_3m = input(true, title = 'Show 3m ', group = 'Timeframe Settings')
show_5m = input(true, title = 'Show 5m', group = 'Timeframe Settings')
show_10m = input(false, title = 'Show 10m', group = 'Timeframe Settings')
show_15m = input(true, title = 'Show 15m', group = 'Timeframe Settings')
show_30m = input(false, title = 'Show 30m', group = 'Timeframe Settings')
show_45m = input(false, title = 'Show 45m', group = 'Timeframe Settings')
show_1h = input(true, title = 'Show 1h', group = 'Timeframe Settings')
show_2h = input(false, title = 'Show 2h', group = 'Timeframe Settings')
show_3h = input(false, title = 'Show 3h', group = 'Timeframe Settings')
show_4h = input(true, title = 'Show 4h', group = 'Timeframe Settings')
show_12h = input(false, title = 'Show 12h', group = 'Timeframe Settings')
show_D = input(true, title = 'Show D', group = 'Timeframe Settings')
show_3D = input(false, title = 'Show 3D', group = 'Timeframe Settings')
show_W = input(false, title = 'Show W', group = 'Timeframe Settings')
show_M = input(false, title = 'Show M', group = 'Timeframe Settings')

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//* CALCULATION *//

var table_position = dashboard_position == 'Top right' ? position.top_right : dashboard_position == 'Top left' ? position.top_left : dashboard_position == 'Top center' ? position.top_center : dashboard_position == 'Bottom right' ? position.bottom_right : dashboard_position == 'Bottom left' ? position.bottom_left : dashboard_position == 'Bottom center' ? position.bottom_center : dashboard_position == 'Middle right' ? position.middle_right : dashboard_position == 'Middle left' ? position.middle_left : position.middle_right

var table_text_size = text_size == 'Normal' ? size.normal : text_size == 'Large' ? size.large : text_size == 'Tiny' ? size.tiny : text_size == 'Small' ? size.small : size.normal

var t = table.new(position = table_position, columns = 3, rows = 20, frame_color = table_color, frame_width = 2, border_color = table_color, border_width = 2)

calc_smma(src, len) =>
    var float smma = na
    smma := na(smma) ? ta.sma(src, len) : (smma[1] * (len - 1) + src) / len
    smma

calc_zlema(src, len) =>
    ema1 = ta.ema(src, len)
    ema2 = ta.ema(ema1, len)
    d = ema1 - ema2
    ema1 + d

check_impulse() =>
    impulse_length = 34
    impulse_strength = 9

    hi = calc_smma(high, impulse_length)
    lo = calc_smma(low, impulse_length)
    mi = calc_zlema(hlc3, impulse_length)

    md = mi > hi ? mi - hi : mi < lo ? mi - lo : 0
    md_prev = mi[1] > hi[1] ? mi[1] - hi[1] : mi[1] < lo[1] ? mi[1] - lo[1] : 0

    sb = ta.sma(md, impulse_strength)
    sb_prev = ta.sma(md_prev, impulse_strength)

    sh = md - sb
    sh_prev = md_prev - sb_prev

    is_impulse = sh != 0 and sh_prev != 0
    is_impulse

get_trend_status() =>
    impulse = catch_flat ? check_impulse() : true
    ema1_current_candle = ta.ema(close, ema1_length)
    ema1_previous_candle = ema1_current_candle[1]
    round = ema1_current_candle > 1000 ? 0 : ema1_current_candle > 10 ? 1 : ema1_current_candle > 0 ? 2 : ema1_current_candle > 0.1 ? 3 : 10
    ema1_str = str.tostring(math.round(ema1_current_candle, round))
    if trend_identification_approach == 'Direction of a single EMA'
        ema1_previous_previous_candle = ema1_current_candle[2]
        trend_current_candle = not impulse ? neutraltrend_indicator : ema1_current_candle > ema1_previous_candle ? uptrend_indicator : ema1_current_candle < ema1_previous_candle ? downtrend_indicator : neutraltrend_indicator
        trend_previous_candle = not impulse ? neutraltrend_indicator : ema1_previous_candle > ema1_previous_previous_candle ? uptrend_indicator : ema1_previous_candle < ema1_previous_previous_candle ? downtrend_indicator : neutraltrend_indicator
        [ema1_str, trend_current_candle, trend_previous_candle]
    else
        ema2_current_candle = ta.ema(close, ema2_length)
        ema2_previous_candle = ema2_current_candle[1]
        trend_current_candle = not impulse ? neutraltrend_indicator : ema1_current_candle > ema2_current_candle ? uptrend_indicator : ema1_current_candle < ema2_current_candle ? downtrend_indicator : neutraltrend_indicator
        trend_previous_candle = not impulse ? neutraltrend_indicator : ema1_previous_candle > ema2_previous_candle ? uptrend_indicator : ema1_previous_candle < ema2_previous_candle ? downtrend_indicator : neutraltrend_indicator
        [ema1_str, trend_current_candle, trend_previous_candle]

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//* TABLE *//

[ema_1m, trend_indicator_1m, _] = request.security(syminfo.tickerid, '1', get_trend_status())
[ema_2m, trend_indicator_2m, _] = request.security(syminfo.tickerid, '2', get_trend_status())
[ema_3m, trend_indicator_3m, _] = request.security(syminfo.tickerid, '3', get_trend_status())
[ema_5m, trend_indicator_5m, _] = request.security(syminfo.tickerid, '5', get_trend_status())
[ema_10m, trend_indicator_10m, _] = request.security(syminfo.tickerid, '10', get_trend_status())
[ema_15m, trend_indicator_15m, _] = request.security(syminfo.tickerid, '15', get_trend_status())
[ema_30m, trend_indicator_30m, _] = request.security(syminfo.tickerid, '30', get_trend_status())
[ema_45m, trend_indicator_45m, _] = request.security(syminfo.tickerid, '45', get_trend_status())
[ema_1h, trend_indicator_1h, _] = request.security(syminfo.tickerid, '60', get_trend_status())
[ema_2h, trend_indicator_2h, _] = request.security(syminfo.tickerid, '120', get_trend_status())
[ema_3h, trend_indicator_3h, _] = request.security(syminfo.tickerid, '180', get_trend_status())
[ema_4h, trend_indicator_4h, _] = request.security(syminfo.tickerid, '240', get_trend_status())
[ema_12h, trend_indicator_12h, _] = request.security(syminfo.tickerid, '720', get_trend_status())
[ema_D, trend_indicator_D, _] = request.security(syminfo.tickerid, 'D', get_trend_status())
[ema_3D, trend_indicator_3D, _] = request.security(syminfo.tickerid, '3D', get_trend_status())
[ema_W, trend_indicator_W, _] = request.security(syminfo.tickerid, 'W', get_trend_status())
[ema_M, trend_indicator_M, _] = request.security(syminfo.tickerid, 'M', get_trend_status())

if barstate.islast
    if show_header
        table.cell(t, 0, 0, 'Timeframe', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 0, 'Trend', text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 0, str.tostring(ema1_length) + ' EMA', text_color = text_color, text_size = table_text_size)

    if show_1m
        table.cell(t, 0, 1, '1m', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 1, trend_indicator_1m, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 1, ema_1m, text_color = text_color, text_size = table_text_size)

    if show_2m
        table.cell(t, 0, 2, '2m', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 2, trend_indicator_2m, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 2, ema_2m, text_color = text_color, text_size = table_text_size)

    if show_3m
        table.cell(t, 0, 3, '3m', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 3, trend_indicator_3m, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 3, ema_3m, text_color = text_color, text_size = table_text_size)

    if show_5m
        table.cell(t, 0, 4, '5m', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 4, trend_indicator_5m, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 4, ema_5m, text_color = text_color, text_size = table_text_size)

    if show_10m
        table.cell(t, 0, 5, '10m', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 5, trend_indicator_10m, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 5, ema_10m, text_color = text_color, text_size = table_text_size)

    if show_15m
        table.cell(t, 0, 6, '15m', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 6, trend_indicator_15m, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 6, ema_15m, text_color = text_color, text_size = table_text_size)

    if show_30m
        table.cell(t, 0, 7, '30m', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 7, trend_indicator_30m, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 7, ema_30m, text_color = text_color, text_size = table_text_size)

    if show_45m
        table.cell(t, 0, 8, '45m', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 8, trend_indicator_45m, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 8, ema_45m, text_color = text_color, text_size = table_text_size)

    if show_1h
        table.cell(t, 0, 9, '1h', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 9, trend_indicator_1h, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 9, ema_1h, text_color = text_color, text_size = table_text_size)

    if show_2h
        table.cell(t, 0, 10, '2h', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 10, trend_indicator_2h, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 10, ema_2h, text_color = text_color, text_size = table_text_size)

    if show_3h
        table.cell(t, 0, 11, '3h', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 11, trend_indicator_3h, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 11, ema_3h, text_color = text_color, text_size = table_text_size)

    if show_4h
        table.cell(t, 0, 12, '4h', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 12, trend_indicator_4h, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 12, ema_4h, text_color = text_color, text_size = table_text_size)

    if show_12h
        table.cell(t, 0, 13, '12h', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 13, trend_indicator_12h, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 13, ema_12h, text_color = text_color, text_size = table_text_size)

    if show_D
        table.cell(t, 0, 14, 'D', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 14, trend_indicator_D, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 14, ema_D, text_color = text_color, text_size = table_text_size)

    if show_3D
        table.cell(t, 0, 15, '3D', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 15, trend_indicator_3D, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 15, ema_3D, text_color = text_color, text_size = table_text_size)

    if show_W
        table.cell(t, 0, 16, 'W', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 16, trend_indicator_W, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 16, ema_W, text_color = text_color, text_size = table_text_size)

    if show_M
        table.cell(t, 0, 17, 'M', text_color = text_color, text_size = table_text_size)
        table.cell(t, 1, 17, trend_indicator_M, text_color = text_color, text_size = table_text_size)
        if show_ema_value
            table.cell(t, 2, 17, ema_M, text_color = text_color, text_size = table_text_size)


//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//text inputs
title = input.string('AG FX TRADING', 'Tittle', group = 'text')
subtitle = input.string('PATIENCE  |  DISCIPLINE  |  FEARLESS', 'Subtitle', group = 'text')
//symbol info
symInfoCheck = input.bool(title = 'Show Symbol Info', defval = true, group = 'watermark position')
symInfo = syminfo.ticker + ' | ' + timeframe.period + (timeframe.isminutes ? 'M' : na)
date = str.tostring(dayofmonth(time_close)) + '/' + str.tostring(month(time_close)) + '/' + str.tostring(year(time_close))
//text positioning
textVPosition = input.string('top', 'Vertical Position', options = ['top', 'middle', 'bottom'], group = 'watermark position')
textHPosition = input.string('center', 'Horizontal Position', options = ['left', 'center', 'right'], group = 'watermark position')
//symbol info positioning
symVPosition = input.string('bottom', 'Vertical Position', options = ['top', 'middle', 'bottom'], group = 'symbol position')
symHPosition = input.string('center', 'Horizontal Position', options = ['left', 'center', 'right'], group = 'symbol position')
//cell size
width = input.int(0, 'Width', minval = 0, maxval = 100, tooltip = 'The width of the cell as a % of the indicator\'s visual space. Optional. By default, auto-adjusts the width based on the text inside the cell. Value 0 has the same effect.', group = 'cell size')
height = input.int(0, 'Height', minval = 0, maxval = 100, tooltip = 'The height of the cell as a % of the indicator\'s visual space. Optional. By default, auto-adjusts the height based on the text inside of the cell. Value 0 has the same effect.', group = 'cell size')
//title settings
c_title = input.color(color.new(color.black, 0), 'Title Color', group = 'title settings')
s_title = input.string('large', 'Title Size', options = ['tiny', 'small', 'normal', 'large', 'huge', 'auto'], group = 'title settings')
a_title = input.string('center', 'Title Alignment', options = ['center', 'left', 'right'], group = 'title settings')
//subtitle settings
c_subtitle = input.color(color.new(color.black, 30), 'Subitle Color', group = 'subtitle settings')
s_subtitle = input.string('normal', 'Subtitle Size', options = ['tiny', 'small', 'normal', 'large', 'huge', 'auto'], group = 'subtitle settings')
a_subtitle = input.string('center', 'Subtitle Alignment', options = ['center', 'left', 'right'], group = 'subtitle settings')

//symbol settings
c_symInfo = input.color(color.new(color.black, 30), 'Subitle Color', group = 'symbol settings')
s_symInfo = input.string('normal', 'Subtitle Size', options = ['tiny', 'small', 'normal', 'large', 'huge', 'auto'], group = 'symbol settings')
a_symInfo = input.string('center', 'Subtitle Alignment', options = ['center', 'left', 'right'], group = 'symbol settings')
c_bg = input.color(color.new(color.blue, 100), 'Background', group = 'background')


//text watermark creation
textWatermark = table.new(textVPosition + '_' + textHPosition, 1, 3)
table.cell(textWatermark, 0, 0, title, width, height, c_title, a_title, text_size = s_title, bgcolor = c_bg)
table.cell(textWatermark, 0, 1, subtitle, width, height, c_subtitle, a_subtitle, text_size = s_subtitle, bgcolor = c_bg)
//symbol info watermark creation
symWatermark = table.new(symVPosition + '_' + symHPosition, 5, 5)
if symInfoCheck == true
    table.cell(symWatermark, 0, 1, symInfo, width, height, c_symInfo, a_symInfo, text_size = s_symInfo, bgcolor = c_bg)
    table.cell(symWatermark, 0, 0, date, width, height, c_symInfo, a_symInfo, text_size = s_symInfo, bgcolor = c_bg)

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// Inputs

string TrendMode = input.string('Normal', 'Trend Mode', options = ['Tight', 'Normal', 'Loose', 'FOMC', 'Net'], group = 'Settings', tooltip = 'There are several trend modes available. The mods are lined up based on the aggressiveness of the ATR. Tight & Normal modes are the going to flip way much often whereas the Loose or FOMC will provide much higher wiggle room. The good rule of thumb to use is to just stick with first two modes when trading less volatile sessions or ranges, and use the other two on fast moving expanding environments. The Net mode provides the combination of all modes in one giant net. Some might prefer this mode since it suits well to the scale in scale out methods. ')
string HTFMode = input.string('Auto', 'HTF Mode', options = ['Auto', 'Manual'], group = 'Settings', tooltip = 'Changes the higher time frame mode. The HTF mode set to auto will automatically change the HTF Trend System time frame for you. The auto mode is choosing the most suitable time frames based on the pre-defined time frame pairs that are the most suitable ones. If you prefer your own time frame choose the manual mode.')
TimeFrameM = input.timeframe('60', 'HTF Aggregation', options = ['1', '2', '3', '5', '10', '15', '20', '30', '45', '60', '120', '180', '240', 'D', '2D', '3D', '4D', 'W', '2W', '3W', 'M', '2M', '3M'], group = 'Settings', tooltip = 'Set the manual time frame for the HTF Trend System.')

ShowTrendBars = input(defval = true, title = 'Show Trend Bars', group = 'Trend Bars', tooltip = 'Trend Bars are based on the DMI and ADX indicators. Whenever the DMI is bearish and ADX is above 20 the candles paint themselfs red. And vice versa for the green candles and bullish DMI. Whenever the ADX falls below the 20, candles are netural which means there is no real trend in place.')
TrendBarBullish = input(#27c22e, title = 'Bullish', group = 'Trend Bars')
TrendBarBearish = input(#ff0000, title = 'Bearish', group = 'Trend Bars')
TrendBarNeutral = input(#434651, title = 'Neutral', group = 'Trend Bars')
ShowTrend = input(defval = true, title = 'Show Trend Line', group = 'Trend Line', tooltip = 'Trend Line is the first part of the L&L Trend System. The trend line is nothing simplier than the 13 exponential moving average. The color of the Trend Line depends on the position of multiple exponential averages and whether they are stacked on top of each other or not.')
TrendBullish = input(#27c22e, title = 'Bullish', group = 'Trend Line')
TrendBearish = input(#ff0000, title = 'Bearish', group = 'Trend Line')
TrendNeutral = input(#434651, title = 'Neutral', group = 'Trend Line')
ShowStop = input(defval = true, title = 'Show Stop Line', group = 'Stop Line', tooltip = 'Stop Line is the main and most important part of the system. It is based on a special ATR calculation that takes into consideration the past ATRs and prices of the 13 EMA. Stop Line provides zones that no moving average can. To make it simple it is something like a moving average that uses the ATR not the average price of the previous bars.')
StopBullish = input(#27c22e, title = 'Bullish', group = 'Stop Line')
StopBearish = input(#ff0000, title = 'Bearish', group = 'Stop Line')
ShowTrend2 = input(defval = false, title = 'Show HTF Trend Line', group = 'Higher Time Frame Trend Line', tooltip = 'Higher Time Frame Trend Line.')
TrendBullish2 = input(#27c22e, title = 'Bullish', group = 'Higher Time Frame Trend Line')
TrendBearish2 = input(#ff0000, title = 'Bearish', group = 'Higher Time Frame Trend Line')
TrendNeutral2 = input(#434651, title = 'Neutral', group = 'Higher Time Frame Trend Line')
ShowStop2 = input(defval = false, title = 'Show HTF Stop Line', group = 'Higher Time Frame Stop Line', tooltip = 'Higher Time Frame Stop Line')
StopBullish2 = input(#27c22e, title = 'Bullish', group = 'Higher Time Frame Stop Line')
StopBearish2 = input(#ff0000, title = 'Bearish', group = 'Higher Time Frame Stop Line')
ShowCloud = input(defval = true, title = 'Show Cloud', group = 'Trend Cloud', tooltip = 'Cloud will paint the area behind the Trend Line and Stop Line with custom color.')
CloudBullish = input(color.rgb(39, 194, 46, 85), title = 'Bullish', group = 'Trend Cloud')
CloudBearish = input(color.rgb(255, 0, 0, 85), title = 'Bearish', group = 'Trend Cloud')
ShowHTFCloud = input(defval = false, title = 'Show HTF Cloud', group = 'Higher Time Frame Trend Cloud', tooltip = 'Higher Time Frame Cloud.')
CloudBullish2 = input(color.rgb(39, 194, 46, 85), title = 'Bullish', group = 'Higher Time Frame Trend Cloud')
CloudBearish2 = input(color.rgb(255, 0, 0, 85), title = 'Bearish', group = 'Higher Time Frame Trend Cloud')

// Trend System (First Time Frame)

ema8 = ta.vwma(close, 8)
ema13 = ta.vwma(close, 13)
ema21 = ta.vwma(close, 21)
ema34 = ta.vwma(close, 34)
emaup = ema8 > ema13 and ema13 > ema21 and ema21 > ema34
emadn = ema8 < ema13 and ema13 < ema21 and ema21 < ema34

Trend = ta.ema(close, 13)
TrendColor = ShowTrend and emadn and close <= Trend ? TrendBearish : ShowTrend and emaup and close >= Trend ? TrendBullish : ShowTrend ? TrendNeutral : na
plot(Trend, title = 'Trend', color = TrendColor, linewidth = 2, editable = false)

ATRLength = if TrendMode == 'Tight'
    60
else if TrendMode == 'Normal'
    80
else if TrendMode == 'Loose'
    100
else if TrendMode == 'FOMC'
    120
else if TrendMode == 'Net'
    140

ATR = ATRLength / 100 * ta.ema(ta.tr(true), 8)
Up = close > Trend + ATR
Down = close < Trend - ATR
var T = 0.0
T := Up ? 1 : Down ? -1 : T[1]

StopLineColor = ShowStop and T == 1 ? StopBullish : ShowStop ? StopBearish : na
plotchar(T == 1 ? Trend - ATR : T == -1 ? Trend + ATR : T[1], title = 'StopLine', char = '-', location = location.absolute, size = size.tiny, color = StopLineColor, editable = false)

ATRA = (ATRLength - 20) / 100 * ta.ema(ta.tr(true), 8)
Up11 = close > Trend + ATRA
Down11 = close < Trend - ATRA
var T11 = 0.0
T11 := Up11 ? 1 : Down11 ? -1 : T11[1]

StopLineColor1 = ShowStop and T11 == 1 ? StopBullish : ShowStop ? StopBearish : na
plotchar(T11 == 1 ? Trend - ATRA : T11 == -1 ? Trend + ATRA : T11[1], title = 'StopLine2', char = '-', location = location.absolute, size = size.tiny, color = StopLineColor1, editable = false)

ATRNET = TrendMode == 'Net' ? (ATRLength - 40) / 100 * ta.ema(ta.tr(true), 8) : na
UpNET = close > Trend + ATRNET
DownNET = close < Trend - ATRNET
var TNET = 0.0
TNET := UpNET ? 1 : DownNET ? -1 : TNET[1]

StopLineColorNET = ShowStop and TNET == 1 ? StopBullish : ShowStop ? StopBearish : na
plotchar(TNET == 1 ? Trend - ATRNET : TNET == -1 ? Trend + ATRNET : TNET[1], title = 'StopLineNET', char = '-', location = location.absolute, size = size.tiny, color = StopLineColorNET, editable = false)

ATRNET1 = TrendMode == 'Net' ? (ATRLength - 60) / 100 * ta.ema(ta.tr(true), 8) : na
UpNET1 = close > Trend + ATRNET1
DownNET1 = close < Trend - ATRNET1
var TNET1 = 0.0
TNET1 := UpNET1 ? 1 : DownNET1 ? -1 : TNET1[1]

StopLineColorNET1 = ShowStop and TNET1 == 1 ? StopBullish : ShowStop ? StopBearish : na
plotchar(TNET1 == 1 ? Trend - ATRNET1 : TNET1 == -1 ? Trend + ATRNET1 : TNET1[1], title = 'StopLineNET1', char = '-', location = location.absolute, size = size.tiny, color = StopLineColorNET1, editable = false)

ATRNET2 = TrendMode == 'Net' ? (ATRLength - 80) / 100 * ta.ema(ta.tr(true), 8) : na
UpNET2 = close > Trend + ATRNET2
DownNET2 = close < Trend - ATRNET2
var TNET2 = 0.0
TNET2 := UpNET2 ? 1 : DownNET2 ? -1 : TNET2[1]

StopLineColorNET2 = ShowStop and TNET2 == 1 ? StopBullish : ShowStop ? StopBearish : na
plotchar(TNET2 == 1 ? Trend - ATRNET2 : TNET2 == -1 ? Trend + ATRNET2 : TNET2[1], title = 'StopLineNET2', char = '-', location = location.absolute, size = size.tiny, color = StopLineColorNET2, editable = false)

// Higher Time Frame Aggregations

TimeFrameA = timeframe.period == '1' ? '5' : timeframe.period == '2' ? '5' : timeframe.period == '3' ? '5' : timeframe.period == '4' ? '5' : timeframe.period == '5' ? '30' : timeframe.period == '10' ? '30' : timeframe.period == '15' ? '30' : timeframe.period == '30' ? '240' : timeframe.period == '60' ? '240' : timeframe.period == '120' ? '240' : timeframe.period == '180' ? 'D' : timeframe.period == '240' ? 'D' : timeframe.period == 'D' ? 'W' : timeframe.period == 'W' ? 'M' : timeframe.period == 'M' ? '3M' : timeframe.period

TimeFrame = if HTFMode == 'Auto'
    TimeFrameA

else if HTFMode == 'Manual'
    TimeFrameM

// Trend System (Second Time Frame)

ema82 = request.security(syminfo.tickerid, TimeFrame, ta.vwma(close, 8))
ema132 = request.security(syminfo.tickerid, TimeFrame, ta.vwma(close, 13))
ema212 = request.security(syminfo.tickerid, TimeFrame, ta.vwma(close, 21))
ema342 = request.security(syminfo.tickerid, TimeFrame, ta.vwma(close, 34))
emaup2 = ema82 > ema132 and ema132 > ema212 and ema212 > ema342
emadn2 = ema82 < ema132 and ema132 < ema212 and ema212 < ema342

Trend2 = request.security(syminfo.tickerid, TimeFrame, ta.ema(close, 13))
TrendColor2 = ShowTrend2 and emadn2 and request.security(syminfo.tickerid, TimeFrame, close) <= Trend2 ? TrendBearish2 : ShowTrend2 and emaup2 and request.security(syminfo.tickerid, TimeFrame, close) >= Trend2 ? TrendBullish2 : ShowTrend2 ? TrendNeutral2 : na
plot(Trend2, title = 'Trend2', color = TrendColor2, linewidth = 2, editable = false)

ATRLength2 = if TrendMode == 'Tight'
    60
else if TrendMode == 'Normal'
    80
else if TrendMode == 'Loose'
    100
else if TrendMode == 'FOMC'
    120
else if TrendMode == 'Net'
    140

ATR2 = ATRLength2 / 100 * request.security(syminfo.tickerid, TimeFrame, ta.ema(ta.tr(true), 8))
Up2 = request.security(syminfo.tickerid, TimeFrame, close) > Trend2 + ATR2
Down2 = request.security(syminfo.tickerid, TimeFrame, close) < Trend2 - ATR2
var T2 = 0.0
T2 := Up2 ? 1 : Down2 ? -1 : T2[1]

StopLineColor2 = ShowStop2 and T2 == 1 ? StopBullish2 : ShowStop2 ? StopBearish2 : na
plotchar(T2 == 1 ? Trend2 - ATR2 : T2 == -1 ? Trend2 + ATR2 : T2[1], title = 'StopLine2', char = '-', location = location.absolute, size = size.tiny, color = StopLineColor2, editable = false)

ATR2A = (ATRLength2 - 20) / 100 * request.security(syminfo.tickerid, TimeFrame, ta.ema(ta.tr(true), 8))
Up2A = request.security(syminfo.tickerid, TimeFrame, close) > Trend2 + ATR2A
Down2A = request.security(syminfo.tickerid, TimeFrame, close) < Trend2 - ATR2A
var T2A = 0.0
T2A := Up2A ? 1 : Down2A[1] ? -1 : T2A[1]

StopLineColor2A = ShowStop2 and T2A == 1 ? StopBullish2 : ShowStop2 ? StopBearish2 : na
plotchar(T2A == 1 ? Trend2 - ATR2A : T2A == -1 ? Trend2 + ATR2A : T2A[1], title = 'StopLine2', char = '-', location = location.absolute, size = size.tiny, color = StopLineColor2A, editable = false)

ATR2ANET = TrendMode == 'Net' ? (ATRLength2 - 40) / 100 * request.security(syminfo.tickerid, TimeFrame, ta.ema(ta.tr(true), 8)) : na
Up2ANET = request.security(syminfo.tickerid, TimeFrame, close) > Trend2 + ATR2ANET
Down2ANET = request.security(syminfo.tickerid, TimeFrame, close) < Trend2 - ATR2ANET
var T2ANET = 0.0
T2ANET := Up2ANET ? 1 : Down2ANET[1] ? -1 : T2ANET[1]

StopLineColor2ANET = ShowStop2 and T2ANET == 1 ? StopBullish2 : ShowStop2 ? StopBearish2 : na
plotchar(T2ANET == 1 ? Trend2 - ATR2ANET : T2ANET == -1 ? Trend2 + ATR2ANET : T2ANET[1], title = 'StopLine2', char = '-', location = location.absolute, size = size.tiny, color = StopLineColor2ANET, editable = false)

ATR2ANET1 = TrendMode == 'Net' ? (ATRLength2 - 60) / 100 * request.security(syminfo.tickerid, TimeFrame, ta.ema(ta.tr(true), 8)) : na
Up2ANET1 = request.security(syminfo.tickerid, TimeFrame, close) > Trend2 + ATR2ANET1
Down2ANET1 = request.security(syminfo.tickerid, TimeFrame, close) < Trend2 - ATR2ANET1
var T2ANET1 = 0.0
T2ANET1 := Up2ANET1 ? 1 : Down2ANET1[1] ? -1 : T2ANET1[1]

StopLineColor2ANET1 = ShowStop2 and T2ANET1 == 1 ? StopBullish2 : ShowStop2 ? StopBearish2 : na
plotchar(T2ANET1 == 1 ? Trend2 - ATR2ANET1 : T2ANET1 == -1 ? Trend2 + ATR2ANET1 : T2ANET1[1], title = 'StopLine2', char = '-', location = location.absolute, size = size.tiny, color = StopLineColor2ANET1, editable = false)

ATR2ANET2 = TrendMode == 'Net' ? (ATRLength2 - 80) / 100 * request.security(syminfo.tickerid, TimeFrame, ta.ema(ta.tr(true), 8)) : na
Up2ANET2 = request.security(syminfo.tickerid, TimeFrame, close) > Trend2 + ATR2ANET2
Down2ANET2 = request.security(syminfo.tickerid, TimeFrame, close) < Trend2 - ATR2ANET2
var T2ANET2 = 0.0
T2ANET2 := Up2ANET2 ? 1 : Down2ANET2[1] ? -1 : T2ANET2[1]

StopLineColor2ANET2 = ShowStop2 and T2ANET2 == 1 ? StopBullish2 : ShowStop2 ? StopBearish2 : na
plotchar(T2ANET2 == 1 ? Trend2 - ATR2ANET2 : T2ANET2 == -1 ? Trend2 + ATR2ANET2 : T2ANET2[1], title = 'StopLine2', char = '-', location = location.absolute, size = size.tiny, color = StopLineColor2ANET2, editable = false)

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


import aybarsm/tac/3 as tac
import aybarsm/Tools/12 as tools
import aybarsm/ToolsMap/2 as tmap

// Inputs
bool showEntryBuy = input.bool(title = 'Buy: Entry', defval = true, group = 'Show Signals', inline = 'signalsBuy')
bool showTpBuy = input.bool(title = 'Buy: TP', defval = true, group = 'Show Signals', inline = 'signalsBuy')
bool showReBuy = input.bool(title = 'Buy: RE', defval = true, group = 'Show Signals', inline = 'signalsBuy')
bool showExitBuy = input.bool(title = 'Buy: Exit', defval = false, group = 'Show Signals', inline = 'signalsBuy')
bool showEntrySell = input.bool(title = 'Sell: Entry', defval = true, group = 'Show Signals', inline = 'signalsSell')
bool showTpSell = input.bool(title = 'Sell: TP', defval = true, group = 'Show Signals', inline = 'signalsSell')
bool showReSell = input.bool(title = 'Sell: RE', defval = true, group = 'Show Signals', inline = 'signalsSell')
bool showExitSell = input.bool(title = 'Sell: Exit', defval = false, group = 'Show Signals', inline = 'signalsSell')

// SMA Filter Toggle
bool useSmaFilter = input.bool(title = 'Enable SMA Filter', defval = true, group = 'Signal Filters')

float psarStart = input.float(title = 'Start', minval = 0.001, defval = 0.02, group = 'PSAR', inline = 'psar')
float psarIncrement = input.float(title = 'Inc', minval = 0.001, defval = 0.02, group = 'PSAR', inline = 'psar')
float psarMax = input.float(title = 'Max', minval = 0.0001, defval = 0.025, group = 'PSAR', inline = 'psar')
float minReProximity = input.float(defval = 0, minval = 0, step = 0.01, maxval = 99.99, title = 'Minimum Re-Entry Proximity Ratio', group = 'Signal Fine-Tuning', tooltip = 'Minimum required proximity ratio to PSAR for re-entry signal')
bool showPsar = input.bool(title = 'PSAR', defval = false, group = 'Show Informative', inline = 'informative')
bool showPsarLagging = input.bool(title = 'Lagging PSAR', defval = true, group = 'Show Informative', inline = 'informative')
bool showPosEntryPrice = input.bool(title = 'Pos Entry', defval = false, group = 'Show Informative', inline = 'informative')

// One-time Variables
var tools.PosInfo pos = na
var tools.Fractals fractals = tools.Fractals.new()
fractals := tools.processFractals(fractals, high, low, 2, 2)

// Each bar Variables
[psar, psar_lagging, psar_fast] = tac.sar(psarStart, psarIncrement, psarMax)
float psarProximity = math.round(math.abs(close - psar_lagging) / psar_lagging * 100, 2)
map<string, bool> chk = map.new<string, bool>()
tools.PosTPInfo lastTp = na
tools.PosREInfo lastRe = na
tools.FracInfo fracSig = na

// Apply SMA filter only if enabled
chk.put('enterBuy', ta.crossover(high, psar_lagging) and (not useSmaFilter or close > sma200))  // Buy signal with optional SMA filter
chk.put('enterSell', ta.crossunder(low, psar_lagging) and (not useSmaFilter or close < sma200))  // Sell signal with optional SMA filter

pos := chk.get('enterBuy') or chk.get('enterSell') ? tools.new_pos(chk.get('enterBuy') ? tools.PosState.buy : tools.PosState.sell, psar_lagging) : pos
chk.put('posBuy', not na(pos) ? pos.state == tools.PosState.buy : false)
chk.put('posSell', not na(pos) ? pos.state == tools.PosState.sell : false)
chk.put('upBoEligible', close[1] > open[1] and close <= open)
chk.put('downBoEligible', close[1] < open[1] and close >= open)

if not na(pos)
    lastTp := array.size(pos.tp) > 0 ? array.last(pos.tp) : na
    lastRe := array.size(pos.re) > 0 ? array.last(pos.re) : na

    if array.size(fractals.hh) > 0 and chk.get('upBoEligible') and (chk.get('posBuy') or chk.get('posSell') and not na(lastTp))
        for i = math.max(0, array.size(fractals.hh) - 2) to array.size(fractals.hh) - 1 by 1
            tools.FracInfo fracHH = array.get(fractals.hh, i)
            if na(fracHH.boPrice) or fracHH.boTime < pos.when
                continue

            chk.put('tpBuy', chk.get('posBuy') and close > pos.price ? na(lastTp) ? true : close > lastTp.price and fracHH.when > lastTp.markWhen : false)
            chk.put('reSell', chk.get('posSell') and psarProximity > minReProximity ? na(lastRe) ? true : lastTp.when > lastRe.when and fracHH.when > lastRe.markWhen : false)
            if chk.get('tpBuy') or chk.get('reSell')
                fracSig := fracHH
                break

    if array.size(fractals.ll) > 0 and chk.get('downBoEligible') and (chk.get('posSell') or chk.get('posBuy') and not na(lastTp))
        for i = math.max(0, array.size(fractals.ll) - 2) to array.size(fractals.ll) - 1 by 1
            tools.FracInfo fracLL = array.get(fractals.ll, i)
            if na(fracLL.boPrice) or fracLL.boTime < pos.when
                continue

            chk.put('tpSell', chk.get('posSell') and close < pos.price ? na(lastTp) ? true : close < lastTp.price and fracLL.when > lastTp.markWhen : false)
            chk.put('reBuy', chk.get('posBuy') and psarProximity > minReProximity ? na(lastRe) ? true : lastTp.when > lastRe.when and fracLL.when > lastRe.markWhen : false)
            if chk.get('tpSell') or chk.get('reBuy')
                fracSig := fracLL
                break

if tmap.def(chk, 'tpBuy') or tmap.def(chk, 'tpSell')
    array.push(pos.tp, tools.PosTPInfo.new(close, time, fracSig.price, fracSig.when))

if tmap.def(chk, 'reBuy') or tmap.def(chk, 'reSell')
    array.push(pos.re, tools.PosREInfo.new(close, time, fracSig.price, fracSig.when))

plotshape(showEntryBuy and chk.get('enterBuy'), style = shape.labelup, location = location.belowbar, color = color.green, size = size.tiny, title = 'BUY', text = 'BUY', textcolor = color.white, force_overlay = true)
plotshape(showEntrySell and chk.get('enterSell'), style = shape.labeldown, location = location.abovebar, color = color.red, size = size.tiny, title = 'SELL', text = 'SELL', textcolor = color.white, force_overlay = true)
plotshape(showExitSell and chk.get('enterBuy'), style = shape.labeldown, location = location.abovebar, color = color.red, size = size.tiny, title = 'EXIT SELL', text = 'EXIT SELL', textcolor = color.white, force_overlay = true)
plotshape(showExitBuy and chk.get('enterSell'), style = shape.labelup, location = location.belowbar, color = color.green, size = size.tiny, title = 'EXIT BUY', text = 'EXIT BUY', textcolor = color.white, force_overlay = true)
plotshape(showTpBuy and tmap.def(chk, 'tpBuy'), style = shape.labeldown, location = location.abovebar, color = color.teal, size = size.tiny, title = 'BUY TP', text = 'TP', textcolor = color.white, force_overlay = true)
plotshape(showTpSell and tmap.def(chk, 'tpSell'), style = shape.labelup, location = location.belowbar, color = color.maroon, size = size.tiny, title = 'SELL TP', text = 'TP', textcolor = color.white, force_overlay = true)
plotshape(showReBuy and tmap.def(chk, 'reBuy'), style = shape.labelup, location = location.belowbar, color = color.olive, size = size.tiny, title = 'BUY RE', text = 'RE', textcolor = color.white, force_overlay = true)
plotshape(showReSell and tmap.def(chk, 'reSell'), style = shape.labeldown, location = location.abovebar, color = color.purple, size = size.tiny, title = 'SELL RE', text = 'RE', textcolor = color.white, force_overlay = true)

plot(showPsar ? psar : na, title = 'PSAR', style = plot.style_line, color = psar < low ? color.lime : color.red)
plot(showPsarLagging ? psar_lagging : na, title = 'Lagging PSAR', color = psar_lagging < low ? color.yellow : color.fuchsia, style = plot.style_line)
plot(showPosEntryPrice and not na(pos) ? pos.price : na, title = 'PosEntryPrice', color = color.white, style = plot.style_line)
plot(not na(pos) ? pos.price : na, title = 'AlertPosEntryValue', color = color.white, style = plot.style_line, display = display.none)

alertcondition(chk.get('enterBuy'), title = 'Enter-Buy', message = 'TTR|Enter-Buy|{{ticker}}|{{exchange}}|{{interval}}|{{time}}|{{plot("AlertPosEntryValue")}}')
alertcondition(chk.get('enterSell'), title = 'Enter-Sell', message = 'TTR|Enter-Sell|{{ticker}}|{{exchange}}|{{interval}}|{{time}}|{{plot("AlertPosEntryValue")}}')
alertcondition(chk.get('enterSell'), title = 'Exit-Buy', message = 'TTR|Exit-Buy|{{ticker}}|{{exchange}}|{{interval}}|{{time}}|{{plot("AlertPosEntryValue")}}')
alertcondition(chk.get('enterBuy'), title = 'Exit-Sell', message = 'TTR|Exit-Sell|{{ticker}}|{{exchange}}|{{interval}}|{{time}}|{{plot("AlertPosEntryValue")}}')
alertcondition(tmap.def(chk, 'tpBuy'), title = 'TP-Buy', message = 'TTR|TP-Buy|{{ticker}}|{{exchange}}|{{interval}}|{{time}}|{{close}}')
alertcondition(tmap.def(chk, 'tpSell'), title = 'TP-Sell', message = 'TTR|TP-Sell|{{ticker}}|{{exchange}}|{{interval}}|{{time}}|{{close}}')
alertcondition(tmap.def(chk, 'reBuy'), title = 'RE-Buy', message = 'TTR|RE-Buy|{{ticker}}|{{exchange}}|{{interval}}|{{time}}|{{close}}')
alertcondition(tmap.def(chk, 'reSell'), title = 'RE-Sell', message = 'TTR|RE-Sell|{{ticker}}|{{exchange}}|{{interval}}|{{time}}|{{close}}')


//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
