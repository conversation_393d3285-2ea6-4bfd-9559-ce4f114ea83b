//@version=5
indicator('Supertrend + Hull Suite + Trendline + EMA', shorttitle='ST + HS + TL + EMA', overlay=true, format=format.price, precision=2, max_labels_count = 500, max_lines_count = 500, max_boxes_count = 500, max_bars_back = 500)

g_sema = 'Show EMA'
g_ema = 'EMA'
g_shs = 'Show Hull Suite'
g_hs = 'Hull Suite'
g_subt = 'Show UT Bot Alerts'
g_ubt = 'UT Bot Alerts'
g_stl = 'Show Trendline'
g_tl = 'Trendline'
g_sst = 'Show Supertrend'
g_st = 'Supertrend'
// Checkbox for showing/hiding EMA
showEMA = input(true, title="Show EMA?", group = g_sema)
// Checkbox for showing/hiding Hull Suite
showHullSuite = input(true, title="Show Hull Suite?", group = g_shs)
visualSwitch = input(true, title='Show as a Band? (Hull Suite)', group = g_shs)
// Checkbox for showing/hiding Supertrend
showSupertrend = input(true, title="Show Supertrend?", group = g_sst)

// =================================================================
// Supertrend
PeriodsSR = input(title='ATR Period', defval=10, group = g_st)
src = input(hl2, title='Source', group = g_st)
Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0, group = g_st)
changeATR = input(title='Change ATR Calculation Method ?', defval=true, group = g_st)
showsignals = input(title='Show Buy/Sell Signals ?', defval=true, group = g_st)
highlighting = input(title='Highlighter On/Off ?', defval=true, group = g_st)


atr2 = ta.sma(ta.tr, PeriodsSR)
atr = changeATR ? ta.atr(PeriodsSR) : atr2
up = src - Multiplier * atr
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = src + Multiplier * atr
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

// Plot Supertrend only if showSupertrend is true
upPlot = plot(showSupertrend ? (trend == 1 ? up : na) : na, title='Up Trend', style=plot.style_linebr, linewidth=2, color=color.new(color.green, 0))
buySignal = trend == 1 and trend[1] == -1
plotshape(buySignal ? up : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.green, 0))
plotshape(buySignal and showsignals ? up : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=color.new(color.green, 0), textcolor=color.new(color.white, 0))
dnPlot = plot(showSupertrend ? (trend == 1 ? na : dn) : na, title='Down Trend', style=plot.style_linebr, linewidth=2, color=color.new(color.red, 0))
sellSignal = trend == -1 and trend[1] == 1
plotshape(sellSignal ? dn : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.red, 0))
plotshape(sellSignal and showsignals ? dn : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=color.new(color.red, 0), textcolor=color.new(color.white, 0))
mPlot = plot(ohlc4, title='', style=plot.style_circles, linewidth=0)
longFillColor = highlighting ? trend == 1 ? color.green : color.white : color.white
shortFillColor = highlighting ? trend == -1 ? color.red : color.white : color.white
fill(mPlot, upPlot, title='UpTrend Highligter', color=longFillColor, transp=90)
fill(mPlot, dnPlot, title='DownTrend Highligter', color=shortFillColor, transp=90)
alertcondition(buySignal, title='SuperTrend Buy', message='SuperTrend Buy!')
alertcondition(sellSignal, title='SuperTrend Sell', message='SuperTrend Sell!')
changeCond = trend != trend[1]
alertcondition(changeCond, title='SuperTrend Direction Change', message='SuperTrend has changed direction!')


// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © KivancOzbilgic

//Trend Magic
// period = input(20, 'CCI period')
// coeff = input(1, 'ATR Multiplier')
// AP = input(5, 'ATR Period')
// ATR = ta.sma(ta.tr, AP)
// srcTM = input(close)
// upT = low - ATR * coeff
// downT = high + ATR * coeff
// MagicTrend = 0.0
// MagicTrend := ta.cci(srcTM, period) >= 0 ? upT < nz(MagicTrend[1]) ? nz(MagicTrend[1]) : upT : downT > nz(MagicTrend[1]) ? nz(MagicTrend[1]) : downT
// color1 = ta.cci(srcTM, period) >= 0 ? #0022FC : #FC0400
// plot(MagicTrend, color=color1, linewidth=3)
// alertcondition(ta.cross(close, MagicTrend), title='Cross Alert', message='Price - MagicTrend Crossing!')
// alertcondition(ta.crossover(low, MagicTrend), title='CrossOver Alarm', message='BUY SIGNAL!')
// alertcondition(ta.crossunder(high, MagicTrend), title='CrossUnder Alarm', message='SELL SIGNAL!')

// Hull Suite
//INPUT
srchs = input(close, title='Source', group = g_hs)
modeSwitch = input.string('Hma', title='Hull Variation', options=['Hma', 'Thma', 'Ehma'], group = g_hs)
length = input(55, title='Length(180-200 for floating S/R , 55 for swing entry)', group = g_hs)
lengthMult = input(1.0, title='Length multiplier (Used to view higher timeframes with straight band)', group = g_hs)

useHtf = input(false, title='Show Hull MA from X timeframe? (good for scalping)', group = g_hs)
htf = input.timeframe('240', title='Higher timeframe', group = g_hs)

switchColor = input(true, 'Color Hull according to trend?', group = g_hs)
candleCol = input(false, title='Color candles based on Hull\'s Trend?', group = g_hs)
thicknesSwitch = input(1, title='Line Thickness', group = g_hs)
transpSwitch = input.int(40, title='Band Transparency', step=5, group = g_hs)

//FUNCTIONS
//HMA
HMA(_srchs, _length) =>
    ta.wma(2 * ta.wma(_srchs, _length / 2) - ta.wma(_srchs, _length), math.round(math.sqrt(_length)))
//EHMA
EHMA(_srchs, _length) =>
    ta.ema(2 * ta.ema(_srchs, _length / 2) - ta.ema(_srchs, _length), math.round(math.sqrt(_length)))
//THMA
THMA(_srchs, _length) =>
    ta.wma(ta.wma(_srchs, _length / 3) * 3 - ta.wma(_srchs, _length / 2) - ta.wma(_srchs, _length), _length)

//SWITCH
Mode(modeSwitch, srchs, len) =>
    modeSwitch == 'Hma' ? HMA(srchs, len) : modeSwitch == 'Ehma' ? EHMA(srchs, len) : modeSwitch == 'Thma' ? THMA(srchs, len / 2) : na

//OUT
_hull = Mode(modeSwitch, srchs, int(length * lengthMult))
HULL = useHtf ? request.security(syminfo.ticker, htf, _hull) : _hull
MHULL = HULL[0]
SHULL = HULL[2]

//COLOR
hullColor = switchColor ? HULL > HULL[2] ? #00ff00 : #ff0000 : #ff9800

//PLOT
///< Frame
Fi1 = plot(showHullSuite ? MHULL : na, title='MHULL', color=hullColor, linewidth=thicknesSwitch, transp=50)
Fi2 = plot(visualSwitch ? SHULL : na, title='SHULL', color=hullColor, linewidth=thicknesSwitch, transp=50)
alertcondition(ta.crossover(MHULL, SHULL), title='Hull trending up.', message='Hull trending up.')
alertcondition(ta.crossover(SHULL, MHULL), title='Hull trending down.', message='Hull trending down.')
///< Ending Filler
fill(Fi1, Fi2, title='Band Filler', color=hullColor, transp=transpSwitch)
///BARCOLOR
barcolor(color=candleCol ? switchColor ? hullColor : na : na)

//Trend Line
// User inputs
prd = input.int(defval=5, title=' Period for Pivot Points (Trendline)', minval=1, maxval=50, group = g_tl)
max_num_of_pivots = input.int(defval=6, title=' Maximum Number of Pivots (Trendline)', minval=5, maxval=10, group = g_tl)
max_lines = input.int(defval=1, title=' Maximum number of trend lines (Trendline)', minval=1, maxval=10, group = g_tl)
show_lines = input.bool(defval=true, title=' Show trend lines (Trendline)', group = g_tl)
show_pivots = input.bool(defval=false, title=' Show Pivot Points (Trendline)', group = g_tl)
sup_line_color = input(defval = color.lime, title = "Colors (Trendline)", inline = "tcol", group = g_tl)
res_line_color = input(defval = color.red, title = "", inline = "tcol", group = g_tl)

float p_h = ta.pivothigh(high, prd, prd)
float p_l = ta.pivotlow(low, prd, prd)

plotshape(p_h and show_pivots, style=shape.triangledown, location=location.abovebar, offset=-prd, size=size.tiny)
plotshape(p_l and show_pivots, style=shape.triangleup, location=location.belowbar, offset=-prd, size=size.tiny)

// Creating array of pivots
var pivots_high = array.new_float(0)
var pivots_low = array.new_float(0)

var high_ind = array.new_int(0)
var low_ind = array.new_int(0)

if p_h
    array.push(pivots_high, p_h)
    array.push(high_ind, bar_index - prd)
    if array.size(pivots_high) > max_num_of_pivots  // limit the array size
        array.shift(pivots_high)
        array.shift(high_ind)

if p_l
    array.push(pivots_low, p_l)
    array.push(low_ind, bar_index - prd)
    if array.size(pivots_low) > max_num_of_pivots  // limit the array size
        array.shift(pivots_low)
        array.shift(low_ind)

// Create arrays to store slopes and lines
var res_lines = array.new_line()
var res_slopes = array.new_float()

len_lines = array.size(res_lines)

if (len_lines >= 1)
    for ind = 0 to len_lines - 1
        to_delete = array.pop(res_lines)
        array.pop(res_slopes)
        line.delete(to_delete)


count_slope(p_h1, p_h2, pos1, pos2) => (p_h2 - p_h1) / (pos2 - pos1)


if array.size(pivots_high) == max_num_of_pivots
    index_of_biggest_slope = 0
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_high, ind1)
            p2 = array.get(pivots_high, ind2)
            pos1 = array.get(high_ind, ind1)
            pos2 = array.get(high_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_high, ind3)
                    pos3 = array.get(high_ind, ind3)
                    if p3 > k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] > p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k


            if ok
                if array.size(res_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                    array.push(res_lines, line)
                    array.push(res_slopes, k)
                else
                    max_slope = array.max(res_slopes)
                    max_slope_ind = array.indexof(res_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k < max_slope
                        line_to_delete = array.get(res_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                        array.insert(res_lines, max_slope_ind, new_line)
                        array.insert(res_slopes, max_slope_ind, k)
                        array.remove(res_lines, max_slope_ind + 1)
                        array.remove(res_slopes, max_slope_ind + 1)

if not show_lines
    len_l = array.size(res_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(res_lines)
            array.pop(res_slopes)
            line.delete(to_delete)



var sup_lines = array.new_line()
var sup_slopes = array.new_float()

len_lines1 = array.size(sup_lines)

if (len_lines1 >= 1)
    for ind = 0 to len_lines1 - 1
        to_delete = array.pop(sup_lines)
        array.pop(sup_slopes)
        line.delete(to_delete)

if array.size(pivots_low) == max_num_of_pivots
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_low, ind1)
            p2 = array.get(pivots_low, ind2)
            pos1 = array.get(low_ind, ind1)
            pos2 = array.get(low_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            // check if pivot points in the middle of two points is lower
            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_low, ind3)
                    pos3 = array.get(low_ind, ind3)
                    if p3 < k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] < p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k

            if ok
                if array.size(sup_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                    array.push(sup_lines, line)
                    array.push(sup_slopes, k)
                else
                    max_slope = array.min(sup_slopes)
                    max_slope_ind = array.indexof(sup_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k > max_slope
                        line_to_delete = array.get(sup_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                        array.insert(sup_lines, max_slope_ind, new_line)
                        array.insert(sup_slopes, max_slope_ind, k)
                        array.remove(sup_lines, max_slope_ind + 1)
                        array.remove(sup_slopes, max_slope_ind + 1)


if not show_lines
    len_l = array.size(sup_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(sup_lines)
            array.pop(sup_slopes)
            line.delete(to_delete)

// Input for EMA length
emaLength = input.int(title="EMA Length", defval=200, minval=2, group = g_ema)
emaSource = input.source(title="EMA Source", defval=close, group = g_ema)

ema = ta.ema(emaSource, emaLength)

// Plot EMA only if showEMA is true
plot(showEMA ? ema : na, color=color.blue, linewidth=2, title="EMA")
