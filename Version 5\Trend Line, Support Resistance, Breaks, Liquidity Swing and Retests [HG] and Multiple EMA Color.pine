// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © <PERSON><PERSON>

//@version=5
indicator("Trend Line, Support Resistance, Breaks, Liquidity Swing and Retests [HG] and Multiple EMA Color", overlay = true, max_labels_count = 500, max_lines_count = 500, max_boxes_count = 500, max_bars_back = 500)

// Support Resistance with Breaks and Retests
g_sr = 'Support and Resistance'
g_c  = 'Conditions'
g_st = 'Styling'
t_r  = 'Bar Confirmation: Generates alerts when candle closes. (1 Candle Later) \n\nHigh & Low: By default, the Break & Retest system uses the current close value to determine a condition, selecting High & Low will make the script utilize these two values instead of the close value. In return, the script won\'t repaint and will yield different results.'
t_rv = 'Whenever a potential retest is detected, the indicator knows that a retest is about to happen. In that given situation, this input grants the ability to raise the limit on how many bars are allowed to be actively checked while a potential retest event is active.\n\nExample, if you see the potential retest label, how many bars do you want that potential retest label to be active for to eventually confirm a retest? This system was implemented to prevent retest alerts from going off 10+ bars later from the potential retest point leading to inaccurate results.'

input_lookback  = input.int(defval = 20, title = 'Lookback Range', minval = 1, tooltip = 'How many bars for a pivot event to occur.', group = g_sr)
input_retSince  = input.int(defval = 2, title = 'Bars Since Breakout', minval = 1, tooltip = 'How many bars since breakout in order to detect a retest.', group = g_sr)
input_retValid  = input.int(defval = 2, title = 'Retest Detection Limiter', minval = 1, tooltip = t_rv, group = g_sr)
input_breakout  = input.bool(defval = true, title = 'Breakouts', group = g_c)
input_retest    = input.bool(defval = true, title = 'Retests', group = g_c)
input_repType   = input.string(defval = 'On', title = 'Repainting', options = ['On', 'Off: Candle Confirmation', 'Off: High & Low'], tooltip = t_r, group = g_c)
input_outL      = input.string(defval = line.style_dotted, title = 'Outline', group = g_st, options = [line.style_dotted, line.style_dashed, line.style_solid])
input_extend    = input.string(defval = extend.none, title = 'Extend', group = g_st, options = [extend.none, extend.right, extend.left, extend.both])
input_labelType = input.string(defval = 'Full', title = 'Label Type', options = ['Full', 'Simple'], group = g_st)
input_labelSize = input.string(defval = size.small, title = 'Label Size', options = [size.tiny, size.small, size.normal, size.large, size.huge], group = g_st)
input_plColor   = input.color(defval = color.red, title = 'Support', inline = 'Color', group = g_st)
input_phColor   = input.color(defval = #089981, title = 'Resistance', inline = 'Color', group = g_st)
input_override  = input.bool(defval = false, title = 'Override Text Color ', inline = 'Override', group = g_st)
input_textColor = input.color(defval = color.white, title = '', inline = 'Override', group = g_st)
bb              = input_lookback

rTon            = input_repType == 'On'
rTcc            = input_repType == 'Off: Candle Confirmation'
rThv            = input_repType == 'Off: High & Low'
breakText       = input_labelType == 'Simple' ? 'Br' : 'Break'

// Pivot Instance
pl = fixnan(ta.pivotlow(low, bb, bb))
ph = fixnan(ta.pivothigh(high, bb, bb))

// Box Height
s_yLoc = low[bb + 1] > low[bb - 1] ? low[bb - 1] : low[bb + 1]
r_yLoc = high[bb + 1] > high[bb - 1] ? high[bb + 1] : high[bb - 1]

//-----------------------------------------------------------------------------
// Functions
//-----------------------------------------------------------------------------
drawBox(condition, y1, y2, color) =>
    var box drawBox = na
    if condition
        box.set_right(drawBox, bar_index - bb)
        drawBox.set_extend(extend.none)
        drawBox := box.new(bar_index - bb, y1, bar_index, y2, color, bgcolor = color.new(color, 90), border_style = input_outL, extend = input_extend)
    [drawBox]

updateBox(box) =>
    if barstate.isconfirmed
        box.set_right(box, bar_index + 5)

breakLabel(y, color, style, textform) => label.new(bar_index, y, textform, textcolor = input_override ? input_textColor : color, style = style, color = color.new(color, 50), size = input_labelSize)
retestCondition(breakout, condition) => ta.barssince(na(breakout)) > input_retSince and condition
repaint(c1, c2, c3) => rTon ? c1 : rThv ? c2 : rTcc ? c3 : na

//-----------------------------------------------------------------------------
// Draw and Update Boxes
//-----------------------------------------------------------------------------
[sBox] = drawBox(ta.change(pl), s_yLoc, pl, input_plColor)
[rBox] = drawBox(ta.change(ph), ph, r_yLoc, input_phColor)
sTop = box.get_top(sBox), rTop = box.get_top(rBox)
sBot = box.get_bottom(sBox), rBot = box.get_bottom(rBox)

updateBox(sBox), updateBox(rBox)

//-----------------------------------------------------------------------------
// Breakout Event
//-----------------------------------------------------------------------------
var bool sBreak = na
var bool rBreak = na
cu = repaint(ta.crossunder(close, box.get_bottom(sBox)), ta.crossunder(low, box.get_bottom(sBox)), ta.crossunder(close, box.get_bottom(sBox)) and barstate.isconfirmed)
co = repaint(ta.crossover(close, box.get_top(rBox)), ta.crossover(high, box.get_top(rBox)), ta.crossover(close, box.get_top(rBox)) and barstate.isconfirmed)

switch
    cu and na(sBreak) =>
        sBreak := true
        if input_breakout
            breakLabel(sBot, input_plColor, label.style_label_upper_right, breakText)
    co and na(rBreak) =>
        rBreak := true
        if input_breakout
            breakLabel(rTop, input_phColor, label.style_label_lower_right, breakText)

if ta.change(pl)
    if na(sBreak)
        box.delete(sBox[1])
    sBreak := na
if ta.change(ph)
    if na(rBreak)
        box.delete(rBox[1])
    rBreak := na

//-----------------------------------------------------------------------------
// Retest Event
//-----------------------------------------------------------------------------
s1 = retestCondition(sBreak, high >= sTop and close <= sBot)                                            // High is GOET top sBox value and the close price is LOET the bottom sBox value.
s2 = retestCondition(sBreak, high >= sTop and close >= sBot and close <= sTop)                          // High is GOET top sBox value and close is GOET the bottom sBox value and closing price is LOET the top sBox value.
s3 = retestCondition(sBreak, high >= sBot and high <= sTop)                                             // High is in between the sBox.
s4 = retestCondition(sBreak, high >= sBot and high <= sTop and close < sBot)                            // High is in between the sBox, and the closing price is below.

r1 = retestCondition(rBreak, low <= rBot and close >= rTop)                                             // Low is LOET bottom rBox value and close is GOET the top sBox value
r2 = retestCondition(rBreak, low <= rBot and close <= rTop and close >= rBot)                           // Low is LOET bottom rBox value and close is LOET the top sBox value and closing price is GOET the bottom rBox value.
r3 = retestCondition(rBreak, low <= rTop and low >= rBot)                                               // Low is in between the rBox.
r4 = retestCondition(rBreak, low <= rTop and low >= rBot and close > rTop)                              // Low is in between the rBox, and the closing price is above.

retestEvent(c1, c2, c3, c4, y1, y2, col, style, pType) =>
    if input_retest
        var bool retOccurred = na
        retActive   = c1 or c2 or c3 or c4
        retEvent    = retActive and not retActive[1]
        retValue    = ta.valuewhen(retEvent, y1, 0)

        if pType == 'ph' ? y2 < ta.valuewhen(retEvent, y2, 0) : y2 > ta.valuewhen(retEvent, y2, 0)
            retEvent := retActive

        // Must be reassigned here just in case the above if statement triggers.
        retValue := ta.valuewhen(retEvent, y1, 0)

        retSince = ta.barssince(retEvent)
        var retLabel = array.new<label>()

        if retEvent
            retOccurred := na
            array.push(retLabel, label.new(bar_index - retSince, y2[retSince], text = input_labelType == 'Simple' ? 'P. Re' : 'Potential Retest', color = color.new(col, 50), style = style, textcolor = input_override ? input_textColor : col, size = input_labelSize))

        if array.size(retLabel) == 2
            label.delete(array.first(retLabel))
            array.shift(retLabel)

        retConditions = pType == 'ph' ? repaint(close >= retValue, high >= retValue, close >= retValue and barstate.isconfirmed) : repaint(close <= retValue, low <= retValue, close <= retValue and barstate.isconfirmed)
        retValid = ta.barssince(retEvent) > 0 and ta.barssince(retEvent) <= input_retValid and retConditions and not retOccurred

        if retValid
            label.new(bar_index - retSince, y2[retSince], text = input_labelType == 'Simple' ? 'Re' : 'Retest', color = color.new(col, 50), style = style, textcolor = input_override ? input_textColor : col, size = input_labelSize)
            retOccurred := true

        if retValid or ta.barssince(retEvent) > input_retValid
            label.delete(array.first(retLabel))

        if pType == 'ph' and ta.change(ph) and retOccurred
            box.set_right(rBox[1], bar_index - retSince)
            retOccurred := na

        if pType == 'pl' and ta.change(pl) and retOccurred
            box.set_right(sBox[1], bar_index - retSince)
            retOccurred := na
        [retValid, retEvent, retValue]

[rRetValid, rRetEvent] = retestEvent(r1, r2, r3, r4, high, low, input_phColor, label.style_label_upper_left, 'ph')
[sRetValid, sRetEvent] = retestEvent(s1, s2, s3, s4, low, high, input_plColor, label.style_label_lower_left, 'pl')


//-----------------------------------------------------------------------------
// Alerts
//-----------------------------------------------------------------------------
alertcondition(ta.change(pl), 'New Support Level')
alertcondition(ta.change(ph), 'New Resistance Level')
alertcondition(ta.barssince(na(sBreak)) == 1, 'Support Breakout')
alertcondition(ta.barssince(na(rBreak)) == 1, 'Resistance Breakout')
alertcondition(sRetValid, 'Support Retest')
alertcondition(sRetEvent, 'Potential Support Retest')
alertcondition(rRetValid, 'Resistance Retest')
alertcondition(rRetEvent, 'Potential Resistance Retest')

AllAlerts(condition, message) =>
    if condition
        alert(message)

AllAlerts(ta.change(pl), 'New Support Level')
AllAlerts(ta.change(ph), 'New Resistance Level')
AllAlerts(ta.barssince(na(sBreak)) == 1, 'Support Breakout')
AllAlerts(ta.barssince(na(rBreak)) == 1, 'Resistance Breakout')
AllAlerts(sRetValid, 'Support Retest')
AllAlerts(sRetEvent, 'Potential Support Retest')
AllAlerts(rRetValid, 'Resistance Retest')
AllAlerts(rRetEvent, 'Potential Resistance Retest')

// EMA Line
// Script settings
emaLength1 = input.int(title="EMA Length 1", defval=5, minval=2)
emaLength2 = input.int(title="EMA Length 2", defval=10, minval=2)
emaLength3 = input.int(title="EMA Length 3", defval=20, minval=2)
emaLength4 = input.int(title="EMA Length 5", defval=50, minval=2)
emaLength5 = input.int(title="EMA Length 6", defval=200, minval=2)
emaSource1 = input.source(title="EMA Source 1", defval=close)
emaSource2 = input.source(title="EMA Source 2", defval=close)
emaSource3 = input.source(title="EMA Source 3", defval=close)
emaSource4 = input.source(title="EMA Source 4", defval=close)
emaSource5 = input.source(title="EMA Source 5", defval=close)

// Get EMA values for different lengths
ema1 = ta.ema(emaSource1, emaLength1)
ema2 = ta.ema(emaSource2, emaLength2)
ema3 = ta.ema(emaSource3, emaLength3)
ema4 = ta.ema(emaSource4, emaLength4)
ema5 = ta.ema(emaSource5, emaLength5)

// Plot EMAs
plot(ema1, color=close[1] > ema1 and close > ema1 ? color.blue : color.red, linewidth=2, title="EMA 1")
plot(ema2, color=close[1] > ema2 and close > ema2 ? color.aqua : color.red, linewidth=2, title="EMA 2")
plot(ema3, color=close[1] > ema3 and close > ema3 ? color.green : color.red, linewidth=2, title="EMA 3")
plot(ema4, color=close[1] > ema4 and close > ema4 ? color.yellow : color.red, linewidth=2, title="EMA 4")
plot(ema5, color=close[1] > ema3 and close > ema5 ? color.orange : color.red, linewidth=2, title="EMA 5")


//Trend Line
// User inputs
prd = input.int(defval=5, title=' Period for Pivot Points', minval=1, maxval=50)
max_num_of_pivots = input.int(defval=6, title=' Maximum Number of Pivots', minval=5, maxval=10)
max_lines = input.int(defval=1, title=' Maximum number of trend lines', minval=1, maxval=10)
show_lines = input.bool(defval=true, title=' Show trend lines')
show_pivots = input.bool(defval=false, title=' Show Pivot Points')
sup_line_color = input(defval = color.lime, title = "Colors", inline = "tcol")
res_line_color = input(defval = color.red, title = "", inline = "tcol")

float p_h = ta.pivothigh(high, prd, prd)
float p_l = ta.pivotlow(low, prd, prd)

plotshape(p_h and show_pivots, style=shape.triangledown, location=location.abovebar, offset=-prd, size=size.tiny)
plotshape(p_l and show_pivots, style=shape.triangleup, location=location.belowbar, offset=-prd, size=size.tiny)

// Creating array of pivots
var pivots_high = array.new_float(0)
var pivots_low = array.new_float(0)

var high_ind = array.new_int(0)
var low_ind = array.new_int(0)

if p_h
    array.push(pivots_high, p_h)
    array.push(high_ind, bar_index - prd)
    if array.size(pivots_high) > max_num_of_pivots  // limit the array size
        array.shift(pivots_high)
        array.shift(high_ind)

if p_l
    array.push(pivots_low, p_l)
    array.push(low_ind, bar_index - prd)
    if array.size(pivots_low) > max_num_of_pivots  // limit the array size
        array.shift(pivots_low)
        array.shift(low_ind)

// Create arrays to store slopes and lines
var res_lines = array.new_line()
var res_slopes = array.new_float()

len_lines = array.size(res_lines)

if (len_lines >= 1)
    for ind = 0 to len_lines - 1
        to_delete = array.pop(res_lines)
        array.pop(res_slopes)
        line.delete(to_delete)


count_slope(p_h1, p_h2, pos1, pos2) => (p_h2 - p_h1) / (pos2 - pos1)


if array.size(pivots_high) == max_num_of_pivots
    index_of_biggest_slope = 0
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_high, ind1)
            p2 = array.get(pivots_high, ind2)
            pos1 = array.get(high_ind, ind1)
            pos2 = array.get(high_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_high, ind3)
                    pos3 = array.get(high_ind, ind3)
                    if p3 > k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] > p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k


            if ok
                if array.size(res_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                    array.push(res_lines, line)
                    array.push(res_slopes, k)
                else
                    max_slope = array.max(res_slopes)
                    max_slope_ind = array.indexof(res_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k < max_slope
                        line_to_delete = array.get(res_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                        array.insert(res_lines, max_slope_ind, new_line)
                        array.insert(res_slopes, max_slope_ind, k)
                        array.remove(res_lines, max_slope_ind + 1)
                        array.remove(res_slopes, max_slope_ind + 1)

if not show_lines
    len_l = array.size(res_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(res_lines)
            array.pop(res_slopes)
            line.delete(to_delete)



var sup_lines = array.new_line()
var sup_slopes = array.new_float()

len_lines1 = array.size(sup_lines)

if (len_lines1 >= 1)
    for ind = 0 to len_lines1 - 1
        to_delete = array.pop(sup_lines)
        array.pop(sup_slopes)
        line.delete(to_delete)

if array.size(pivots_low) == max_num_of_pivots
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_low, ind1)
            p2 = array.get(pivots_low, ind2)
            pos1 = array.get(low_ind, ind1)
            pos2 = array.get(low_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            // check if pivot points in the middle of two points is lower
            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_low, ind3)
                    pos3 = array.get(low_ind, ind3)
                    if p3 < k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] < p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k

            if ok
                if array.size(sup_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                    array.push(sup_lines, line)
                    array.push(sup_slopes, k)
                else
                    max_slope = array.min(sup_slopes)
                    max_slope_ind = array.indexof(sup_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k > max_slope
                        line_to_delete = array.get(sup_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                        array.insert(sup_lines, max_slope_ind, new_line)
                        array.insert(sup_slopes, max_slope_ind, k)
                        array.remove(sup_lines, max_slope_ind + 1)
                        array.remove(sup_slopes, max_slope_ind + 1)


if not show_lines
    len_l = array.size(sup_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(sup_lines)
            array.pop(sup_slopes)
            line.delete(to_delete)

// Liquidity Swing LuxAlgo
// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo
//------------------------------------------------------------------------------
//Settings
//-----------------------------------------------------------------------------{
lengthLS = input(14, 'Pivot Lookback Liquidity Swing')

area = input.string('Wick Extremity', 'Swing Area', options = ['Wick Extremity', 'Full Range'])

intraPrecision = input(false, 'Intrabar Precision', inline = 'intrabar')
intrabarTf = input.timeframe('1', ''              , inline = 'intrabar')

filterOptions = input.string('Count', 'Filter Areas By', options = ['Count', 'Volume'], inline = 'filter')
filterValue   = input.float(0, ''                                            , inline = 'filter')

//Style
showTop      = input(true, 'Swing High'              , inline = 'top', group = 'Style')
topCss       = input(color.red, ''                   , inline = 'top', group = 'Style')
topAreaCss   = input(color.new(color.red, 50), 'Area', inline = 'top', group = 'Style')

showBtm      = input(true, 'Swing Low'                , inline = 'btm', group = 'Style')
btmCss       = input(color.teal, ''                   , inline = 'btm', group = 'Style')
btmAreaCss   = input(color.new(color.teal, 50), 'Area', inline = 'btm', group = 'Style')

labelSize = input.string('Tiny', 'Labels Size', options = ['Tiny', 'Small', 'Normal'], group = 'Style')

//-----------------------------------------------------------------------------}
//Functions
//-----------------------------------------------------------------------------{
n = bar_index

get_data()=> [high, low, volume]

[h, l, v] = request.security_lower_tf(syminfo.tickerid, intrabarTf, get_data())

get_counts(condition, top, btm)=>
    var count = 0
    var vol = 0.

    if condition
        count := 0
        vol := 0.
    else
        if intraPrecision
            if n > lengthLS
                if array.size(v[lengthLS]) > 0
                    for [index, element] in v[lengthLS]
                        vol += array.get(l[lengthLS], index) < top and array.get(h[lengthLS], index) > btm ? element : 0
        else
            vol += low[lengthLS] < top and high[lengthLS] > btm ? volume[lengthLS] : 0

        count += low[lengthLS] < top and high[lengthLS] > btm ? 1 : 0

    [count, vol]

set_label(count, vol, x, y, css, lbl_style)=>
    var label lbl = na
    var label_size = switch labelSize
        'Tiny' => size.tiny
        'Small' => size.small
        'Normal' => size.normal

    target = switch filterOptions
        'Count'  => count
        'Volume' => vol

    if ta.crossover(target, filterValue)
        lbl := label.new(x, y, str.tostring(vol, format.volume)
          , style = lbl_style
          , size = label_size
          , color = #00000000
          , textcolor = css)

    if target > filterValue
        label.set_text(lbl, str.tostring(vol, format.volume))

set_level(condition, crossed, value, count, vol, css)=>
    var line lvl = na

    target = switch filterOptions
        'Count'  => count
        'Volume' => vol

    if condition
        if target[1] < filterValue[1]
            line.delete(lvl[1])
        else if not crossed[1]
            line.set_x2(lvl, n - lengthLS)

        lvl := line.new(n - lengthLS, value, n, value
          , color = na)

    if not crossed[1]
        line.set_x2(lvl, n+3)

    if crossed and not crossed[1]
        line.set_x2(lvl, n)
        line.set_style(lvl, line.style_dashed)

    if target > filterValue
        line.set_color(lvl, css)

set_zone(condition, x, top, btm, count, vol, css)=>
    var box bx = na

    target = switch filterOptions
        'Count'  => count
        'Volume' => vol

    if ta.crossover(target, filterValue)
        bx := box.new(x, top, x + count, btm
          , border_color = na
          , bgcolor = css)

    if target > filterValue
        box.set_right(bx, x + count)

//-----------------------------------------------------------------------------}
//Global variables
//-----------------------------------------------------------------------------{
//Pivot high
var float ph_ls_top = na
var float ph_ls_btm = na
var bool  ph_ls_crossed = na
var       ph_ls_x1 = 0
var box   ph_ls_bx = box.new(na,na,na,na
  , bgcolor = color.new(topAreaCss, 80)
  , border_color = na)

//Pivot low
var float pl_ls_top = na
var float pl_ls_btm = na
var bool  pl_ls_crossed = na
var       pl_ls_x1 = 0
var box   pl_ls_bx = box.new(na,na,na,na
  , bgcolor = color.new(btmAreaCss, 80)
  , border_color = na)

//-----------------------------------------------------------------------------}
//Display pivot high levels/blocks
//-----------------------------------------------------------------------------{
ph_ls = ta.pivothigh(lengthLS, lengthLS)

//Get ph_ls counts
[ph_ls_count, ph_ls_vol] = get_counts(ph_ls, ph_ls_top, ph_ls_btm)

//Set ph_ls area and level
if ph_ls and showTop
    ph_ls_top := high[lengthLS]
    ph_ls_btm := switch area
        'Wick Extremity' => math.max(close[lengthLS], open[lengthLS])
        'Full Range' => low[lengthLS]

    ph_ls_x1 := n - lengthLS
    ph_ls_crossed := false

    box.set_lefttop(ph_ls_bx, ph_ls_x1, ph_ls_top)
    box.set_rightbottom(ph_ls_bx, ph_ls_x1, ph_ls_btm)
else
    ph_ls_crossed := close > ph_ls_top ? true : ph_ls_crossed

    if ph_ls_crossed
        box.set_right(ph_ls_bx, ph_ls_x1)
    else
        box.set_right(ph_ls_bx, n+3)

if showTop
    //Set ph_ls zone
    set_zone(ph_ls, ph_ls_x1, ph_ls_top, ph_ls_btm, ph_ls_count, ph_ls_vol, topAreaCss)

    //Set ph_ls level
    set_level(ph_ls, ph_ls_crossed, ph_ls_top, ph_ls_count, ph_ls_vol, topCss)

    //Set ph_ls label
    set_label(ph_ls_count, ph_ls_vol, ph_ls_x1, ph_ls_top, topCss, label.style_label_down)

//-----------------------------------------------------------------------------}
//Display pivot low levels/blocks
//-----------------------------------------------------------------------------{
pl_ls = ta.pivotlow(lengthLS, lengthLS)

//Get pl_ls counts
[pl_ls_count, pl_ls_vol] = get_counts(pl_ls, pl_ls_top, pl_ls_btm)

//Set pl_ls area and level
if pl_ls and showBtm
    pl_ls_top := switch area
        'Wick Extremity' => math.min(close[lengthLS], open[lengthLS])
        'Full Range' => high[lengthLS]
    pl_ls_btm := low[lengthLS]

    pl_ls_x1 := n - lengthLS
    pl_ls_crossed := false

    box.set_lefttop(pl_ls_bx, pl_ls_x1, pl_ls_top)
    box.set_rightbottom(pl_ls_bx, pl_ls_x1, pl_ls_btm)
else
    pl_ls_crossed := close < pl_ls_btm ? true : pl_ls_crossed

    if pl_ls_crossed
        box.set_right(pl_ls_bx, pl_ls_x1)
    else
        box.set_right(pl_ls_bx, n+3)

if showBtm
    //Set pl_ls zone
    set_zone(pl_ls, pl_ls_x1, pl_ls_top, pl_ls_btm, pl_ls_count, pl_ls_vol, btmAreaCss)

    //Set pl_ls level
    set_level(pl_ls, pl_ls_crossed, pl_ls_btm, pl_ls_count, pl_ls_vol, btmCss)

    //Set pl_ls labels
    set_label(pl_ls_count, pl_ls_vol, pl_ls_x1, pl_ls_btm, btmCss, label.style_label_up)

//-----------------------------------------------------------------------------}