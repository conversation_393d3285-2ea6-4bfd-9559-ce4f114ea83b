//@version=5
indicator("Trend Signals with TP & SL + MA + Trendline",shorttitle = "Trend  Signals with TP & SL + MA + Trendline", overlay = true)
src =  input(hl2, title="Source",group = "Trend Continuation Signals with TP & SL")
Multiplier = input.float(2,title="Sensitivity (0.5 - 5)", step=0.1, defval=2, minval=0.5, maxval=5,group = "Trend Continuation Signals with TP & SL")
atrPeriods = input.int(14,title="ATR Length", defval=10,group = "Trend Continuation Signals with TP & SL")
atrCalcMethod= input.string("Method 1",title = "ATR Calculation Methods",options = ["Method 1","Method 2"],group = "Trend Continuation Signals with TP & SL")
cloud_val = input.int(10,title = "Cloud Moving Average Length", defval = 10, minval = 5, maxval = 500,group = "Trend Continuation Signals with TP & SL")
stopLossVal  = input.float(2.0, title="Stop Loss Percent (0 for Disabling)", minval=0,group = "Trend Continuation Signals with TP & SL")
showBuySellSignals = input.bool(true,title="Show Buy/Sell Signals", defval=true,group = "Trend Continuation Signals with TP & SL")
showMovingAverageCloud = input.bool(true, title="Show Cloud MA",group = "Trend Continuation Signals with TP & SL")

percent(nom, div) =>
    100 * nom / div

src1 = ta.hma(open, 5)[1]
src2 = ta.hma(close, 12)
momm1 = ta.change(src1)
momm2 = ta.change(src2)
f1(m, n) => m >= n ? m : 0.0
f2(m, n) => m >= n ? 0.0 : -m
m1 = f1(momm1, momm2)
m2 = f2(momm1, momm2)
sm1 = math.sum(m1, 1)
sm2 = math.sum(m2, 1)

cmoCalc = percent(sm1-sm2, sm1+sm2)

hh = ta.highest(2)
h1 = ta.dev(hh, 2) ? na : hh
hpivot = fixnan(h1)
ll = ta.lowest(2)
l1 = ta.dev(ll, 2) ? na : ll
lpivot = fixnan(l1)

rsiCalc = ta.rsi(close,9)
lowPivot =  lpivot
highPivot =  hpivot

sup = rsiCalc < 25 and cmoCalc > 50  and lowPivot
res = rsiCalc > 75 and cmoCalc < -50  and highPivot

atr2 = ta.sma(ta.tr, atrPeriods)
atr= atrCalcMethod == "Method 1" ? ta.atr(atrPeriods) : atr2
up=src-(Multiplier*atr)
up1 = nz(up[1],up)
up := close[1] > up1 ? math.max(up,up1) : up
dn=src+(Multiplier*atr)
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn

trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
buySignal = trend == 1 and trend[1] == -1
sellSignal = trend == -1 and trend[1] == 1

pos = 0.0
pos:= buySignal? 1 : sellSignal ? -1 : pos[1]

longCond  = buySignal and pos[1]!= 1
shortCond = sellSignal and pos[1]!=-1

entryOfLongPosition  = ta.valuewhen(longCond , close, 0)
entryOfShortPosition = ta.valuewhen(shortCond, close, 0)

sl  = stopLossVal > 0 ? stopLossVal / 100 : 99999

stopLossForLong  = entryOfLongPosition  * (1 - sl)
stopLossForShort = entryOfShortPosition * (1 + sl)

takeProfitForLong1R  = entryOfLongPosition  * (1 + sl)
takeProfitForShort1R = entryOfShortPosition * (1 - sl)

takeProfitForLong2R  = entryOfLongPosition  * (1 + sl*2)
takeProfitForShort2R = entryOfShortPosition * (1 - sl*2)

takeProfitForLong3R  = entryOfLongPosition  * (1 + sl*3)
takeProfitForShort3R = entryOfShortPosition * (1 - sl*3)

long_sl  = low < stopLossForLong  and pos[1]==1
short_sl = high> stopLossForShort and pos[1]==-1

takeProfitForLongFinal  = high>takeProfitForLong3R  and pos[1]==1
takeProfitForShortFinal = low <takeProfitForShort3R and pos[1]==-1

if long_sl or short_sl or takeProfitForLongFinal or takeProfitForShortFinal
    pos:=0

lindex = ta.valuewhen(longCond, bar_index, 0)
sindex= ta.valuewhen(shortCond, bar_index, 0)

entryColor = pos==1? color.blue : color.orange

if barstate.islast and pos!=0
    lineEntry  = line.new(bar_index, pos>0?entryOfLongPosition :entryOfShortPosition , pos>0?lindex:sindex, pos>0?entryOfLongPosition :entryOfShortPosition , color=entryColor  )
    line.delete(lineEntry[1])

    stopLine  = line.new(bar_index, pos>0?stopLossForLong :stopLossForShort , pos>0?lindex:sindex, pos>0?stopLossForLong :stopLossForShort , color=color.red  )
    tpLine1 = line.new(bar_index, pos>0?takeProfitForLong1R:takeProfitForShort1R, pos>0?lindex:sindex, pos>0?takeProfitForLong1R:takeProfitForShort1R, color=color.green)
    tpLine2 = line.new(bar_index, pos>0?takeProfitForLong2R:takeProfitForShort2R, pos>0?lindex:sindex, pos>0?takeProfitForLong2R:takeProfitForShort2R, color=color.green)
    tpLine3 = line.new(bar_index, pos>0?takeProfitForLong3R:takeProfitForShort3R, pos>0?lindex:sindex, pos>0?takeProfitForLong3R:takeProfitForShort3R, color=color.green)
    line.delete(stopLine [1])
    line.delete(tpLine1[1])
    line.delete(tpLine2[1])
    line.delete(tpLine3[1])

    labelEntry  = label.new(bar_index, pos>0?entryOfLongPosition :entryOfShortPosition , color=entryColor  , textcolor=#000000, style=label.style_label_left, text="Entry Price: " + str.tostring(pos>0?entryOfLongPosition :entryOfShortPosition ))
    label.delete(labelEntry[1])

    labelStop  = label.new(bar_index, pos>0?stopLossForLong :stopLossForShort , color=color.red  , textcolor=#000000, style=label.style_label_left, text="Stop Loss Price: " + str.tostring(math.round((pos>0?stopLossForLong :stopLossForShort) *100)/100))
    labelTp1 = label.new(bar_index, pos>0?takeProfitForLong1R:takeProfitForShort1R, color=color.green, textcolor=#000000, style=label.style_label_left, text="Take Profit 1: " +str.tostring(math.round((pos>0?takeProfitForLong1R:takeProfitForShort1R) * 100)/100))
    labelTp2 = label.new(bar_index, pos>0?takeProfitForLong2R:takeProfitForShort2R, color=color.green, textcolor=#000000, style=label.style_label_left, text="Take Profit 2: " + str.tostring(math.round((pos>0?takeProfitForLong2R:takeProfitForShort2R) * 100)/100))
    labelTp3 = label.new(bar_index, pos>0?takeProfitForLong3R:takeProfitForShort3R, color=color.green, textcolor=#000000, style=label.style_label_left, text="Take Profit 3: " + str.tostring(math.round((pos>0?takeProfitForLong3R:takeProfitForShort3R) * 100)/100))
    label.delete(labelStop [1])
    label.delete(labelTp1[1])
    label.delete(labelTp2[1])
    label.delete(labelTp3[1])

changeCond = trend != trend[1]
smaSrcHigh = ta.ema(high,cloud_val)
smaSrcLow = ta.ema(low, cloud_val)
[macdLine, signalLine, histLine] = ta.macd(close, 12, 26, 9)
plot_high = plot(showMovingAverageCloud? smaSrcHigh : na, color = na, transp = 1, editable = false)
plot_low  = plot(showMovingAverageCloud? smaSrcLow  : na, color = na, transp = 1, editable = false)

plotshape(longCond ? up : na, title="UpTrend Begins", location=location.belowbar, style=shape.circle, size=size.tiny, color=color.new(color.teal,transp = 50) )
plotshape(longCond and showBuySellSignals ? up : na, title="Buy", text="Buy", location=location.belowbar, style=shape.labelup, size=size.tiny, color=color.new(color.teal,transp = 50), textcolor=color.white )
plotshape(shortCond ? dn : na, title="DownTrend Begins", location=location.abovebar, style=shape.circle, size=size.tiny, color=color.new(color.red,transp = 50) )
plotshape(shortCond and showBuySellSignals ? dn : na, title="Sell", text="Sell", location=location.abovebar, style=shape.labeldown, size=size.tiny, color=color.new(color.red,transp = 50), textcolor=color.white)

fill(plot_high, plot_low, color = (macdLine > 0) and (macdLine[0] > macdLine[1]) ? color.new(color.aqua,transp = 85) : na, title = "Positive Cloud Uptrend")
fill(plot_high, plot_low, color = macdLine > 0 and macdLine[0] < macdLine[1]     ? color.new(color.aqua,transp = 85) : na, title = "Positive Cloud  Downtrend")
fill(plot_high, plot_low, color = macdLine < 0 and macdLine[0] < macdLine[1]     ? color.new(color.red,transp = 85) : na, title = "Negative Cloud  Uptrend")
fill(plot_high, plot_low, color = macdLine < 0 and macdLine[0] > macdLine[1]     ? color.new(color.red,transp = 85) : na, title = "Negative Cloud Downtrend")
mPlot = plot(ohlc4, title="", style=plot.style_circles, linewidth=0)

alertcondition(changeCond, title="Trend Direction Change ", message="Trend direction has changed ! ")

alertLongText = str.tostring(syminfo.ticker) + " BUY ALERT! " +
                  "Entry Price: " + str.tostring(entryOfLongPosition) +
                  ", Take Profit 1: " + str.tostring(takeProfitForLong1R) +
                  ", Take Profit 2: " + str.tostring(takeProfitForLong2R) +
                  ", Take Profit 3: " + str.tostring(takeProfitForLong3R) +
                  ", Stop Loss Price: " + str.tostring(stopLossForLong)

alertShortText = str.tostring(syminfo.ticker) + " SELL ALERT!" +
                  ", Entry Price: " + str.tostring(entryOfShortPosition) +
                  ", Take Profit 1: " + str.tostring(takeProfitForShort1R) +
                  ", Take Profit 2: " + str.tostring(takeProfitForShort2R) +
                  ", Take Profit 3: " + str.tostring(takeProfitForShort3R) +
                  ", Stop Loss Price: " + str.tostring(stopLossForShort)

longJson = '{"content": "' + alertLongText + '"}'
shortJson = '{"content": "' + alertShortText + '"}'

if longCond
    alert(longJson, alert.freq_once_per_bar_close)

if shortCond
    alert(shortJson, alert.freq_once_per_bar_close)

//Multi MA with Custom Type
f_ma(source, length, mtype) =>
    mtype == 'SMA' ? ta.sma(source, length) : mtype == 'EMA' ? ta.ema(source, length) : ta.wma(source, length)

gr_ma = 'Optional MA\'s'
t_ma1 = 'MA #1'
t_ma2 = 'MA #2'
t_ma3 = 'MA #3'

// Inputs for MA1
show_ma1 = input.bool(false, t_ma1, inline = t_ma1, group = gr_ma)
ma1_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma1, group = gr_ma)
ma1_source = input.source(close, '', inline = t_ma1, group = gr_ma)
ma1_length = input.int(20, '', minval = 1, inline = t_ma1, group = gr_ma)
ma1_color = #59ff00
ma1 = f_ma(ma1_source, ma1_length, ma1_type)

// Inputs for MA2
show_ma2 = input.bool(false, t_ma2, inline = t_ma2, group = gr_ma)
ma2_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma2, group = gr_ma)
ma2_source = input.source(close, '', inline = t_ma2, group = gr_ma)
ma2_length = input.int(50, '', minval = 1, inline = t_ma2, group = gr_ma)
ma2_color = #ff7b5a
ma2 = f_ma(ma2_source, ma2_length, ma2_type)

// Inputs for MA3
show_ma3 = input.bool(false, t_ma3, inline = t_ma3, group = gr_ma)
ma3_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma3, group = gr_ma)
ma3_source = input.source(close, '', inline = t_ma3, group = gr_ma)
ma3_length = input.int(100, '', minval = 1, inline = t_ma3, group = gr_ma)
ma3_color = #ffc100
ma3 = f_ma(ma3_source, ma3_length, ma3_type)

// Plot and fill logic for MA1
plot0_ma1 = plot(ma1_source, display = display.none, editable = false)
css_ma1 = ma1_source > ma1 ? #0cb51a : #ff1100
plot1_ma1 = plot(show_ma1 ? ma1 : na, title = t_ma1, color = css_ma1, linewidth = 1, display = show_ma1 ? display.all : display.none)
fill_css_ma1 = ma1_source > ma1 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma1, plot1_ma1, color = fill_css_ma1, title = 'Fill MA1')

// Plot and fill logic for MA2
plot0_ma2 = plot(ma2_source, display = display.none, editable = false)
css_ma2 = ma2_source > ma2 ? #0cb51a : #ff1100
plot1_ma2 = plot(show_ma2 ? ma2 : na, title = t_ma2, color = css_ma2, linewidth = 1, display = show_ma2 ? display.all : display.none)
fill_css_ma2 = ma2_source > ma2 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma2, plot1_ma2, color = fill_css_ma2, title = 'Fill MA2')

// Plot and fill logic for MA3
plot0_ma3 = plot(ma3_source, display = display.none, editable = false)
css_ma3 = ma3_source > ma3 ? #0cb51a : #ff1100
plot1_ma3 = plot(show_ma3 ? ma3 : na, title = t_ma3, color = css_ma3, linewidth = 1, display = show_ma3 ? display.all : display.none)
fill_css_ma3 = ma3_source > ma3 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma3, plot1_ma3, color = fill_css_ma3, title = 'Fill MA3')

//Trend Line
// User inputs
g_tl = 'Trendline'
show_lines = input.bool(defval=false, title=' Show trend lines (Trendline)', tooltip="Aktifkan untuk memunculkan Trendline.", group = g_tl)
show_pivots = input.bool(defval=false, title=' Show Pivot Points (Trendline)', tooltip="Aktifkan untuk memunculkan Pivot.", group = g_tl)
prd = input.int(defval=20, title=' Period for Pivot Points (Trendline)', tooltip="Atur panjang trendline 1-50 bar ke belakang.", minval=1, maxval=50, group = g_tl)
max_num_of_pivots = input.int(defval=6, title=' Maximum Number of Pivots (Trendline)', tooltip="Maksimal High/Low pivot.", minval=5, maxval=10, group = g_tl)
max_lines = input.int(defval=1, title=' Maximum number of trend lines (Trendline)', tooltip="Berapa trendline yang mau dimunculkan.", minval=1, maxval=10, group = g_tl)
sup_line_color = input(defval = color.lime, title = "Colors (Trendline)", inline = "tcol", group = g_tl)
res_line_color = input(defval = color.red, title = "", inline = "tcol", group = g_tl)

float p_h = ta.pivothigh(high, prd, prd)
float p_l = ta.pivotlow(low, prd, prd)

plotshape(p_h and show_pivots, style=shape.triangledown, location=location.abovebar, offset=-prd, size=size.tiny)
plotshape(p_l and show_pivots, style=shape.triangleup, location=location.belowbar, offset=-prd, size=size.tiny)

// Creating array of pivots
var pivots_high = array.new_float(0)
var pivots_low = array.new_float(0)

var high_ind = array.new_int(0)
var low_ind = array.new_int(0)

if p_h
    array.push(pivots_high, p_h)
    array.push(high_ind, bar_index - prd)
    if array.size(pivots_high) > max_num_of_pivots  // limit the array size
        array.shift(pivots_high)
        array.shift(high_ind)

if p_l
    array.push(pivots_low, p_l)
    array.push(low_ind, bar_index - prd)
    if array.size(pivots_low) > max_num_of_pivots  // limit the array size
        array.shift(pivots_low)
        array.shift(low_ind)

// Create arrays to store slopes and lines
var res_lines = array.new_line()
var res_slopes = array.new_float()

len_lines = array.size(res_lines)

if (len_lines >= 1)
    for ind = 0 to len_lines - 1
        to_delete = array.pop(res_lines)
        array.pop(res_slopes)
        line.delete(to_delete)


count_slope(p_h1, p_h2, pos1, pos2) => (p_h2 - p_h1) / (pos2 - pos1)


if array.size(pivots_high) == max_num_of_pivots
    index_of_biggest_slope = 0
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_high, ind1)
            p2 = array.get(pivots_high, ind2)
            pos1 = array.get(high_ind, ind1)
            pos2 = array.get(high_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_high, ind3)
                    pos3 = array.get(high_ind, ind3)
                    if p3 > k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] > p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k


            if ok
                if array.size(res_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                    array.push(res_lines, line)
                    array.push(res_slopes, k)
                else
                    max_slope = array.max(res_slopes)
                    max_slope_ind = array.indexof(res_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k < max_slope
                        line_to_delete = array.get(res_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                        array.insert(res_lines, max_slope_ind, new_line)
                        array.insert(res_slopes, max_slope_ind, k)
                        array.remove(res_lines, max_slope_ind + 1)
                        array.remove(res_slopes, max_slope_ind + 1)

if not show_lines
    len_l = array.size(res_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(res_lines)
            array.pop(res_slopes)
            line.delete(to_delete)



var sup_lines = array.new_line()
var sup_slopes = array.new_float()

len_lines1 = array.size(sup_lines)

if (len_lines1 >= 1)
    for ind = 0 to len_lines1 - 1
        to_delete = array.pop(sup_lines)
        array.pop(sup_slopes)
        line.delete(to_delete)

if array.size(pivots_low) == max_num_of_pivots
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_low, ind1)
            p2 = array.get(pivots_low, ind2)
            pos1 = array.get(low_ind, ind1)
            pos2 = array.get(low_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            // check if pivot points in the middle of two points is lower
            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_low, ind3)
                    pos3 = array.get(low_ind, ind3)
                    if p3 < k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] < p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k

            if ok
                if array.size(sup_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                    array.push(sup_lines, line)
                    array.push(sup_slopes, k)
                else
                    max_slope = array.min(sup_slopes)
                    max_slope_ind = array.indexof(sup_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k > max_slope
                        line_to_delete = array.get(sup_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                        array.insert(sup_lines, max_slope_ind, new_line)
                        array.insert(sup_slopes, max_slope_ind, k)
                        array.remove(sup_lines, max_slope_ind + 1)
                        array.remove(sup_slopes, max_slope_ind + 1)


if not show_lines
    len_l = array.size(sup_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(sup_lines)
            array.pop(sup_slopes)
            line.delete(to_delete)