//@version=5
indicator("Support Resistance, Breaks and Retests [HG] and Multi EMA with labels (Any timeframe)", overlay = true, max_boxes_count = 500, max_labels_count = 500)

g_sr = 'Support and Resistance'
g_c  = 'Conditions'
g_st = 'Styling'
t_r  = 'Bar Confirmation: Generates alerts when candle closes. (1 Candle Later) \n\nHigh & Low: By default, the Break & Retest system uses the current close value to determine a condition, selecting High & Low will make the script utilize these two values instead of the close value. In return, the script won\'t repaint and will yield different results.'
t_rv = 'Whenever a potential retest is detected, the indicator knows that a retest is about to happen. In that given situation, this input grants the ability to raise the limit on how many bars are allowed to be actively checked while a potential retest event is active.\n\nExample, if you see the potential retest label, how many bars do you want that potential retest label to be active for to eventually confirm a retest? This system was implemented to prevent retest alerts from going off 10+ bars later from the potential retest point leading to inaccurate results.'

input_lookback  = input.int(defval = 20, title = 'Lookback Range', minval = 1, tooltip = 'How many bars for a pivot event to occur.', group = g_sr)
input_retSince  = input.int(defval = 2, title = 'Bars Since Breakout', minval = 1, tooltip = 'How many bars since breakout in order to detect a retest.', group = g_sr)
input_retValid  = input.int(defval = 2, title = 'Retest Detection Limiter', minval = 1, tooltip = t_rv, group = g_sr)
input_breakout  = input.bool(defval = true, title = 'Breakouts', group = g_c)
input_retest    = input.bool(defval = true, title = 'Retests', group = g_c)
input_repType   = input.string(defval = 'On', title = 'Repainting', options = ['On', 'Off: Candle Confirmation', 'Off: High & Low'], tooltip = t_r, group = g_c)
input_outL      = input.string(defval = line.style_dotted, title = 'Outline', group = g_st, options = [line.style_dotted, line.style_dashed, line.style_solid])
input_extend    = input.string(defval = extend.none, title = 'Extend', group = g_st, options = [extend.none, extend.right, extend.left, extend.both])
input_labelType = input.string(defval = 'Full', title = 'Label Type', options = ['Full', 'Simple'], group = g_st)
input_labelSize = input.string(defval = size.small, title = 'Label Size', options = [size.tiny, size.small, size.normal, size.large, size.huge], group = g_st)
input_plColor   = input.color(defval = color.red, title = 'Support', inline = 'Color', group = g_st)
input_phColor   = input.color(defval = #089981, title = 'Resistance', inline = 'Color', group = g_st)
input_override  = input.bool(defval = false, title = 'Override Text Color ', inline = 'Override', group = g_st)
input_textColor = input.color(defval = color.white, title = '', inline = 'Override', group = g_st)
bb              = input_lookback

rTon            = input_repType == 'On'
rTcc            = input_repType == 'Off: Candle Confirmation'
rThv            = input_repType == 'Off: High & Low'
breakText       = input_labelType == 'Simple' ? 'Br' : 'Break'

// Pivot Instance
pl = fixnan(ta.pivotlow(low, bb, bb))
ph = fixnan(ta.pivothigh(high, bb, bb))

// Box Height
s_yLoc = low[bb + 1] > low[bb - 1] ? low[bb - 1] : low[bb + 1]
r_yLoc = high[bb + 1] > high[bb - 1] ? high[bb + 1] : high[bb - 1]

//-----------------------------------------------------------------------------
// Functions
//-----------------------------------------------------------------------------
drawBox(condition, y1, y2, color) =>
    var box drawBox = na
    if condition
        box.set_right(drawBox, bar_index - bb)
        drawBox.set_extend(extend.none)
        drawBox := box.new(bar_index - bb, y1, bar_index, y2, color, bgcolor = color.new(color, 90), border_style = input_outL, extend = input_extend)
    [drawBox]

updateBox(box) =>
    if barstate.isconfirmed
        box.set_right(box, bar_index + 5)

breakLabel(y, color, style, textform) => label.new(bar_index, y, textform, textcolor = input_override ? input_textColor : color, style = style, color = color.new(color, 50), size = input_labelSize)
retestCondition(breakout, condition) => ta.barssince(na(breakout)) > input_retSince and condition
repaint(c1, c2, c3) => rTon ? c1 : rThv ? c2 : rTcc ? c3 : na

//-----------------------------------------------------------------------------
// Draw and Update Boxes
//-----------------------------------------------------------------------------
[sBox] = drawBox(ta.change(pl), s_yLoc, pl, input_plColor)
[rBox] = drawBox(ta.change(ph), ph, r_yLoc, input_phColor)
sTop = box.get_top(sBox), rTop = box.get_top(rBox)
sBot = box.get_bottom(sBox), rBot = box.get_bottom(rBox)

updateBox(sBox), updateBox(rBox)

//-----------------------------------------------------------------------------
// Breakout Event
//-----------------------------------------------------------------------------
var bool sBreak = na
var bool rBreak = na
cu = repaint(ta.crossunder(close, box.get_bottom(sBox)), ta.crossunder(low, box.get_bottom(sBox)), ta.crossunder(close, box.get_bottom(sBox)) and barstate.isconfirmed)
co = repaint(ta.crossover(close, box.get_top(rBox)), ta.crossover(high, box.get_top(rBox)), ta.crossover(close, box.get_top(rBox)) and barstate.isconfirmed)

switch
    cu and na(sBreak) =>
        sBreak := true
        if input_breakout
            breakLabel(sBot, input_plColor, label.style_label_upper_right, breakText)
    co and na(rBreak) =>
        rBreak := true
        if input_breakout
            breakLabel(rTop, input_phColor, label.style_label_lower_right, breakText)

if ta.change(pl)
    if na(sBreak)
        box.delete(sBox[1])
    sBreak := na
if ta.change(ph)
    if na(rBreak)
        box.delete(rBox[1])
    rBreak := na

//-----------------------------------------------------------------------------
// Retest Event
//-----------------------------------------------------------------------------
s1 = retestCondition(sBreak, high >= sTop and close <= sBot)                                            // High is GOET top sBox value and the close price is LOET the bottom sBox value.
s2 = retestCondition(sBreak, high >= sTop and close >= sBot and close <= sTop)                          // High is GOET top sBox value and close is GOET the bottom sBox value and closing price is LOET the top sBox value.
s3 = retestCondition(sBreak, high >= sBot and high <= sTop)                                             // High is in between the sBox.
s4 = retestCondition(sBreak, high >= sBot and high <= sTop and close < sBot)                            // High is in between the sBox, and the closing price is below.

r1 = retestCondition(rBreak, low <= rBot and close >= rTop)                                             // Low is LOET bottom rBox value and close is GOET the top sBox value
r2 = retestCondition(rBreak, low <= rBot and close <= rTop and close >= rBot)                           // Low is LOET bottom rBox value and close is LOET the top sBox value and closing price is GOET the bottom rBox value.
r3 = retestCondition(rBreak, low <= rTop and low >= rBot)                                               // Low is in between the rBox.
r4 = retestCondition(rBreak, low <= rTop and low >= rBot and close > rTop)                              // Low is in between the rBox, and the closing price is above.

retestEvent(c1, c2, c3, c4, y1, y2, col, style, pType) =>
    if input_retest
        var bool retOccurred = na
        retActive   = c1 or c2 or c3 or c4
        retEvent    = retActive and not retActive[1]
        retValue    = ta.valuewhen(retEvent, y1, 0)

        if pType == 'ph' ? y2 < ta.valuewhen(retEvent, y2, 0) : y2 > ta.valuewhen(retEvent, y2, 0)
            retEvent := retActive

        // Must be reassigned here just in case the above if statement triggers.
        retValue := ta.valuewhen(retEvent, y1, 0)

        retSince = ta.barssince(retEvent)
        var retLabel = array.new<label>()

        if retEvent
            retOccurred := na
            array.push(retLabel, label.new(bar_index - retSince, y2[retSince], text = input_labelType == 'Simple' ? 'P. Re' : 'Potential Retest', color = color.new(col, 50), style = style, textcolor = input_override ? input_textColor : col, size = input_labelSize))

        if array.size(retLabel) == 2
            label.delete(array.first(retLabel))
            array.shift(retLabel)

        retConditions = pType == 'ph' ? repaint(close >= retValue, high >= retValue, close >= retValue and barstate.isconfirmed) : repaint(close <= retValue, low <= retValue, close <= retValue and barstate.isconfirmed)
        retValid = ta.barssince(retEvent) > 0 and ta.barssince(retEvent) <= input_retValid and retConditions and not retOccurred

        if retValid
            label.new(bar_index - retSince, y2[retSince], text = input_labelType == 'Simple' ? 'Re' : 'Retest', color = color.new(col, 50), style = style, textcolor = input_override ? input_textColor : col, size = input_labelSize)
            retOccurred := true

        if retValid or ta.barssince(retEvent) > input_retValid
            label.delete(array.first(retLabel))

        if pType == 'ph' and ta.change(ph) and retOccurred
            box.set_right(rBox[1], bar_index - retSince)
            retOccurred := na

        if pType == 'pl' and ta.change(pl) and retOccurred
            box.set_right(sBox[1], bar_index - retSince)
            retOccurred := na
        [retValid, retEvent, retValue]

[rRetValid, rRetEvent] = retestEvent(r1, r2, r3, r4, high, low, input_phColor, label.style_label_upper_left, 'ph')
[sRetValid, sRetEvent] = retestEvent(s1, s2, s3, s4, low, high, input_plColor, label.style_label_lower_left, 'pl')


//-----------------------------------------------------------------------------
// Alerts
//-----------------------------------------------------------------------------
alertcondition(ta.change(pl), 'New Support Level')
alertcondition(ta.change(ph), 'New Resistance Level')
alertcondition(ta.barssince(na(sBreak)) == 1, 'Support Breakout')
alertcondition(ta.barssince(na(rBreak)) == 1, 'Resistance Breakout')
alertcondition(sRetValid, 'Support Retest')
alertcondition(sRetEvent, 'Potential Support Retest')
alertcondition(rRetValid, 'Resistance Retest')
alertcondition(rRetEvent, 'Potential Resistance Retest')

AllAlerts(condition, message) =>
    if condition
        alert(message)

AllAlerts(ta.change(pl), 'New Support Level')
AllAlerts(ta.change(ph), 'New Resistance Level')
AllAlerts(ta.barssince(na(sBreak)) == 1, 'Support Breakout')
AllAlerts(ta.barssince(na(rBreak)) == 1, 'Resistance Breakout')
AllAlerts(sRetValid, 'Support Retest')
AllAlerts(sRetEvent, 'Potential Support Retest')
AllAlerts(rRetValid, 'Resistance Retest')
AllAlerts(rRetEvent, 'Potential Resistance Retest')

get_timeframe_title(simple string tf = "") =>
    chartTf = timeframe.isminutes == true and timeframe.multiplier > 59 ? (timeframe.multiplier/60 % 2 == 0 ? str.tostring(timeframe.multiplier/60)+"h" : str.tostring(timeframe.multiplier)+"m") : timeframe.isminutes == true ? str.tostring(timeframe.multiplier)+"m" : timeframe.period
    result = tf == "" ? "" : request.security(syminfo.tickerid, tf, chartTf)

distance = input(2, "Label Distance")

show_prices = input.bool(true, "Show price labels")

ema1_enable = input.bool(true, "Enable EMA 1", group = "EMA Options")
ema1_timeframe = input.timeframe("", "EMA 1 Timeframe", group = "EMA Options")
ema1_len = input.int(20, minval=1, title="EMA 1 Length", group = "EMA Options")
ema1_out = ta.ema(close, ema1_len)
ema1_color_input = input(color.new(#A5D6A7, 20), title="EMA 1 Color", group = "EMA Options")
ema1_color = ema1_enable ? ema1_color_input : na

ema2_enable = input.bool(true, "Enable EMA 2", group = "EMA Options")
ema2_timeframe = input.timeframe("", "EMA 2 Timeframe", group = "EMA Options")
ema2_len = input.int(50, minval=1, title="EMA 2 Length", group = "EMA Options")
ema2_out = ta.ema(close, ema2_len)
ema2_color_input = input(color.new(#F2AF29, 20), title="EMA 2 Color", group = "EMA Options")
ema2_color = ema2_enable ? ema2_color_input : na

ema3_enable = input.bool(true, "Enable EMA 3", group = "EMA Options")
ema3_timeframe = input.timeframe("", "EMA 3 Timeframe", group = "EMA Options")
ema3_len = input.int(200, minval=1, title="EMA 3 Length", group = "EMA Options")
ema3_out = ta.ema(close, ema3_len)
ema3_color_input = input(color.new(#725AC1, 20), title="EMA 3 Color", group = "EMA Options")
ema3_color = ema3_enable ? ema3_color_input : na

ema4_enable = input.bool(false, "Enable EMA 4", group = "EMA Options")
ema4_timeframe = input.timeframe("", "EMA 4 Timeframe", group = "EMA Options")
ema4_len = input.int(100, minval=1, title="EMA 4 Length", group = "EMA Options")
ema4_out = ta.ema(close, ema4_len)
ema4_color_input = input(color.new(#FE5E41, 20), title="EMA 4 Color", group = "EMA Options")
ema4_color = ema4_enable ? ema4_color_input : na

ema5_enable = input.bool(false, "Enable EMA 5", group = "EMA Options")
ema5_timeframe = input.timeframe("", "EMA 5 Timeframe", group = "EMA Options")
ema5_len = input.int(400, minval=1, title="EMA 5 Length", group = "EMA Options")
ema5_out = ta.ema(close, ema5_len)
ema5_color_input = input(color.new(#243E36, 20), title="EMA 5 Color", group = "EMA Options")
ema5_color = ema5_enable ? ema5_color_input : na

ema6_enable = input.bool(false, "Enable EMA 6", group = "EMA Options")
ema6_timeframe = input.timeframe("", "EMA 6 Timeframe", group = "EMA Options")
ema6_len = input.int(9, minval=1, title="EMA 6 Length", group = "EMA Options")
ema6_out = ta.ema(close, ema6_len)
ema6_color_input = input(color.new(#3B3923, 20), title="EMA 6 Color", group = "EMA Options")
ema6_color = ema6_enable ? ema6_color_input : na

ema7_enable = input.bool(false, "Enable EMA 7", group = "EMA Options")
ema7_timeframe = input.timeframe("", "EMA 7 Timeframe", group = "EMA Options")
ema7_len = input.int(26, minval=1, title="EMA 7 Length", group = "EMA Options")
ema7_out = ta.ema(close, ema7_len)
ema7_color_input = input(color.new(#3A7CA5, 20), title="EMA 7 Color", group = "EMA Options")
ema7_color = ema7_enable ? ema7_color_input : na

ema8_enable = input.bool(false, "Enable EMA 8", group = "EMA Options")
ema8_timeframe = input.timeframe("", "EMA 8 Timeframe", group = "EMA Options")
ema8_len = input.int(12, minval=1, title="EMA 8 Length", group = "EMA Options")
ema8_out = ta.ema(close, ema8_len)
ema8_color_input = input(color.new(#795663, 20), title="EMA 8 Color", group = "EMA Options")
ema8_color = ema8_enable ? ema8_color_input : na

securityNoRepaint(sym, tf, src) => request.security(sym, tf, src[barstate.isrealtime ? 1 : 0])[barstate.isrealtime ? 0 : 1]
ema1_tf = securityNoRepaint(syminfo.tickerid, ema1_timeframe, ema1_out)
ema2_tf = securityNoRepaint(syminfo.tickerid, ema2_timeframe, ema2_out)
ema3_tf = securityNoRepaint(syminfo.tickerid, ema3_timeframe, ema3_out)
ema4_tf = securityNoRepaint(syminfo.tickerid, ema4_timeframe, ema4_out)
ema5_tf = securityNoRepaint(syminfo.tickerid, ema5_timeframe, ema5_out)
ema6_tf = securityNoRepaint(syminfo.tickerid, ema6_timeframe, ema6_out)
ema7_tf = securityNoRepaint(syminfo.tickerid, ema7_timeframe, ema7_out)
ema8_tf = securityNoRepaint(syminfo.tickerid, ema8_timeframe, ema8_out)
plot(ema1_tf, title="EMA", color=ema1_color, linewidth=2, style=plot.style_line)
plot(ema2_tf, title="EMA", color=ema2_color, linewidth=2, style=plot.style_line)
plot(ema3_tf, title="EMA", color=ema3_color, linewidth=2, style=plot.style_line)
plot(ema4_tf, title="EMA", color=ema4_color, linewidth=2, style=plot.style_line)
plot(ema5_tf, title="EMA", color=ema5_color, linewidth=2, style=plot.style_line)
plot(ema6_tf, title="EMA", color=ema6_color, linewidth=2, style=plot.style_line)
plot(ema7_tf, title="EMA", color=ema7_color, linewidth=2, style=plot.style_line)
plot(ema8_tf, title="EMA", color=ema8_color, linewidth=2, style=plot.style_line)

var label ema1_label = na
ema1_label_size = size.normal
var label ema2_label = na
ema2_label_size = size.normal
var label ema3_label = na
ema3_label_size = size.normal
var label ema4_label = na
ema4_label_size = size.normal
var label ema5_label = na
ema5_label_size = size.normal
var label ema6_label = na
ema6_label_size = size.normal
var label ema7_label = na
ema7_label_size = size.normal
var label ema8_label = na
ema8_label_size = size.normal

timeframe_text = get_timeframe_title(ema1_timeframe)
timeframe2_text = get_timeframe_title(ema2_timeframe)
timeframe3_text = get_timeframe_title(ema3_timeframe)
timeframe4_text = get_timeframe_title(ema4_timeframe)
timeframe5_text = get_timeframe_title(ema5_timeframe)
timeframe6_text = get_timeframe_title(ema6_timeframe)
timeframe7_text = get_timeframe_title(ema7_timeframe)
timeframe8_text = get_timeframe_title(ema8_timeframe)

label_x = time + math.round(ta.change(time)*distance)

ema1_p_str = show_prices ? " - "+str.tostring(math.round_to_mintick(ema1_tf)) : ""
ema2_p_str = show_prices ? " - "+str.tostring(math.round_to_mintick(ema2_tf)) : ""
ema3_p_str = show_prices ? " - "+str.tostring(math.round_to_mintick(ema3_tf)) : ""
ema4_p_str = show_prices ? " - "+str.tostring(math.round_to_mintick(ema4_tf)) : ""
ema5_p_str = show_prices ? " - "+str.tostring(math.round_to_mintick(ema5_tf)) : ""
ema6_p_str = show_prices ? " - "+str.tostring(math.round_to_mintick(ema6_tf)) : ""
ema7_p_str = show_prices ? " - "+str.tostring(math.round_to_mintick(ema7_tf)) : ""
ema8_p_str = show_prices ? " - "+str.tostring(math.round_to_mintick(ema8_tf)) : ""

labelpadding = "                                         "

ema1_label_txt = timeframe_text == "" ? labelpadding+"EMA "+str.tostring(ema1_len)+ema1_p_str : labelpadding+"EMA "+str.tostring(ema1_len)+" ("+timeframe_text+")"+ema1_p_str
ema1_label := label.new(label_x, ema1_tf, text = ema1_label_txt, xloc=xloc.bar_time, color = ema1_color, textcolor = ema1_color, style = label.style_none, size=size.normal)
label.delete(ema1_label[1])

ema2_label_txt = timeframe2_text == "" ? labelpadding+"EMA "+str.tostring(ema2_len)+ema2_p_str : labelpadding+"EMA "+str.tostring(ema2_len)+" ("+timeframe2_text+")"+ema2_p_str
ema2_label := label.new(label_x, ema2_tf, text = ema2_label_txt, xloc=xloc.bar_time, color = ema2_color, textcolor = ema2_color, style = label.style_none, size=size.normal)
label.delete(ema2_label[1])

ema3_label_txt = timeframe3_text == "" ? labelpadding+"EMA "+str.tostring(ema3_len)+ema3_p_str : labelpadding+"EMA "+str.tostring(ema3_len)+" ("+timeframe3_text+")"+ema3_p_str
ema3_label := label.new(label_x, ema3_tf, text = ema3_label_txt, xloc=xloc.bar_time, color = ema3_color, textcolor = ema3_color, style = label.style_none, size=size.normal)
label.delete(ema3_label[1])

ema4_label_txt = timeframe4_text == "" ? labelpadding+"EMA "+str.tostring(ema4_len)+ema4_p_str : labelpadding+"EMA "+str.tostring(ema4_len)+" ("+timeframe4_text+")"+ema4_p_str
ema4_label := label.new(label_x, ema4_tf, text = ema4_label_txt, xloc=xloc.bar_time, color = ema4_color, textcolor = ema4_color, style = label.style_none, size=size.normal)
label.delete(ema4_label[1])

ema5_label_txt = timeframe5_text == "" ? labelpadding+"EMA "+str.tostring(ema5_len)+ema5_p_str : labelpadding+"EMA "+str.tostring(ema5_len)+" ("+timeframe5_text+")"+ema5_p_str
ema5_label := label.new(label_x, ema5_tf, text = ema5_label_txt, xloc=xloc.bar_time, color = ema5_color, textcolor = ema5_color, style = label.style_none, size=size.normal)
label.delete(ema5_label[1])

ema6_label_txt = timeframe6_text == "" ? labelpadding+"EMA "+str.tostring(ema6_len)+ema6_p_str : labelpadding+"EMA "+str.tostring(ema6_len)+" ("+timeframe6_text+")"+ema6_p_str
ema6_label := label.new(label_x, ema6_tf, text = ema6_label_txt, xloc=xloc.bar_time, color = ema6_color, textcolor = ema6_color, style = label.style_none, size=size.normal)
label.delete(ema6_label[1])

ema7_label_txt = timeframe7_text == "" ? labelpadding+"EMA "+str.tostring(ema7_len)+ema7_p_str : labelpadding+"EMA "+str.tostring(ema7_len)+" ("+timeframe7_text+")"+ema7_p_str
ema7_label := label.new(label_x, ema7_tf, text = ema7_label_txt, xloc=xloc.bar_time, color = ema7_color, textcolor = ema7_color, style = label.style_none, size=size.normal)
label.delete(ema7_label[1])

ema8_label_txt = timeframe8_text == "" ? labelpadding+"EMA "+str.tostring(ema8_len)+ema8_p_str : labelpadding+"EMA "+str.tostring(ema8_len)+" ("+timeframe8_text+")"+ema8_p_str
ema8_label := label.new(label_x, ema8_tf, text = ema8_label_txt, xloc=xloc.bar_time, color = ema8_color, textcolor = ema8_color, style = label.style_none, size=size.normal)
label.delete(ema8_label[1])