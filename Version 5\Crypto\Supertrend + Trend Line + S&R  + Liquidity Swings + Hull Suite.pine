//==========================================================================================
// Smart Money Concepts (LuxAlgo)
//==========================================================================================
indicator("Supertrend + Trend Line + S&R  + Liquidity Swings + Hull Suite"
  , overlay = true
  , max_labels_count = 500
  , max_lines_count = 500
  , max_boxes_count = 500
  , max_bars_back = 500)

//==========================================================================================
// Colored EMA
//==========================================================================================

// // Script settings
// emaLength1 = input.int(title="EMA Length 1", defval=13, minval=2)
// emaLength2 = input.int(title="EMA Length 2", defval=21, minval=2)
// emaLength3 = input.int(title="EMA Length 3", defval=33, minval=2)
// emaLength4 = input.int(title="EMA Length 4", defval=55, minval=2)
// emaSource1 = input.source(title="EMA Source 1", defval=close)
// emaSource2 = input.source(title="EMA Source 2", defval=close)
// emaSource3 = input.source(title="EMA Source 3", defval=close)
// emaSource4 = input.source(title="EMA Source 4", defval=close)

// // Get EMA values for different lengths
// ema1 = ta.ema(emaSource1, emaLength1)
// ema2 = ta.ema(emaSource2, emaLength2)
// ema3 = ta.ema(emaSource3, emaLength3)
// ema4 = ta.ema(emaSource4, emaLength4)

// // Plot EMAs
// plot(ema1, color=close[1] > ema1 and close > ema1 ? color.green : color.red, linewidth=2, title="EMA 1")
// plot(ema2, color=close[1] > ema2 and close > ema2 ? color.yellow : color.red, linewidth=2, title="EMA 2")
// plot(ema3, color=close[1] > ema3 and close > ema3 ? color.orange : color.red, linewidth=2, title="EMA 3")
// plot(ema4, color=close[1] > ema4 and close > ema4 ? color.orange : color.red, linewidth=2, title="EMA 4")

//==========================================================================================
// Colored EMA End
//==========================================================================================

//==========================================================================================
// Liquidity Swings (LuxAlgo)
//==========================================================================================
// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo

//@version=5

//------------------------------------------------------------------------------
//Settings
//-----------------------------------------------------------------------------{
// lengthLS = input(14, 'Pivot Lookback')

// area = input.string('Wick Extremity', 'Swing Area', options = ['Wick Extremity', 'Full Range'])

// intraPrecision = input(false, 'Intrabar Precision', inline = 'intrabar')
// intrabarTf = input.timeframe('1', ''              , inline = 'intrabar')

// filterOptions = input.string('Count', 'Filter Areas By', options = ['Count', 'Volume'], inline = 'filter')
// filterValue   = input.float(0, ''                                            , inline = 'filter')

// //Style
// showTop      = input(true, 'Swing High'              , inline = 'top', group = 'Style')
// topCss       = input(color.red, ''                   , inline = 'top', group = 'Style')
// topAreaCss   = input(color.new(color.red, 50), 'Area', inline = 'top', group = 'Style')

// showBtm      = input(true, 'Swing Low'                , inline = 'btm', group = 'Style')
// btmCss       = input(color.teal, ''                   , inline = 'btm', group = 'Style')
// btmAreaCss   = input(color.new(color.teal, 50), 'Area', inline = 'btm', group = 'Style')

// labelSize = input.string('Tiny', 'Labels Size', options = ['Tiny', 'Small', 'Normal'], group = 'Style')

// //-----------------------------------------------------------------------------}
// //Functions
// //-----------------------------------------------------------------------------{
// nLS = bar_index

// get_data()=> [high, low, volume]

// [h, l, v] = request.security_lower_tf(syminfo.tickerid, intrabarTf, get_data())

// get_counts(condition, top, btm)=>
//     var count = 0
//     var vol = 0.

//     if condition
//         count := 0
//         vol := 0.
//     else
//         if intraPrecision
//             if nLS > lengthLS
//                 if array.size(v[lengthLS]) > 0
//                     for [index, element] in v[lengthLS]
//                         vol += array.get(l[lengthLS], index) < top and array.get(h[lengthLS], index) > btm ? element : 0
//         else
//             vol += low[lengthLS] < top and high[lengthLS] > btm ? volume[lengthLS] : 0

//         count += low[lengthLS] < top and high[lengthLS] > btm ? 1 : 0

//     [count, vol]

// set_label(count, vol, x, y, css, lbl_style)=>
//     var label lbl = na
//     var label_size = switch labelSize
//         'Tiny' => size.tiny
//         'Small' => size.small
//         'Normal' => size.normal

//     target = switch filterOptions
//         'Count'  => count
//         'Volume' => vol

//     if ta.crossover(target, filterValue)
//         lbl := label.new(x, y, str.tostring(vol, format.volume)
//           , style = lbl_style
//           , size = label_size
//           , color = #00000000
//           , textcolor = css)

//     if target > filterValue
//         label.set_text(lbl, str.tostring(vol, format.volume))

// set_level(condition, crossed, value, count, vol, css)=>
//     var line lvl = na

//     target = switch filterOptions
//         'Count'  => count
//         'Volume' => vol

//     if condition
//         if target[1] < filterValue[1]
//             line.delete(lvl[1])
//         else if not crossed[1]
//             line.set_x2(lvl, nLS - lengthLS)

//         lvl := line.new(nLS - lengthLS, value, nLS, value
//           , color = na)

//     if not crossed[1]
//         line.set_x2(lvl, nLS+3)

//     if crossed and not crossed[1]
//         line.set_x2(lvl, nLS)
//         line.set_style(lvl, line.style_dashed)

//     if target > filterValue
//         line.set_color(lvl, css)

// set_zone(condition, x, top, btm, count, vol, css)=>
//     var box bx = na

//     target = switch filterOptions
//         'Count'  => count
//         'Volume' => vol

//     if ta.crossover(target, filterValue)
//         bx := box.new(x, top, x + count, btm
//           , border_color = na
//           , bgcolor = css)

//     if target > filterValue
//         box.set_right(bx, x + count)

// //-----------------------------------------------------------------------------}
// //Global variables
// //-----------------------------------------------------------------------------{
// //Pivot high
// var float ph_top = na
// var float ph_btm = na
// var bool  ph_crossed = na
// var       ph_x1 = 0
// var box   ph_bx = box.new(na,na,na,na
//   , bgcolor = color.new(topAreaCss, 80)
//   , border_color = na)

// //Pivot low
// var float pl_top = na
// var float pl_btm = na
// var bool  pl_crossed = na
// var       pl_x1 = 0
// var box   pl_bx = box.new(na,na,na,na
//   , bgcolor = color.new(btmAreaCss, 80)
//   , border_color = na)

// //-----------------------------------------------------------------------------}
// //Display pivot high levels/blocks
// //-----------------------------------------------------------------------------{
// ph = ta.pivothigh(lengthLS, lengthLS)

// //Get ph counts
// [ph_count, ph_vol] = get_counts(ph, ph_top, ph_btm)

// //Set ph area and level
// if ph and showTop
//     ph_top := high[lengthLS]
//     ph_btm := switch area
//         'Wick Extremity' => math.max(close[lengthLS], open[lengthLS])
//         'Full Range' => low[lengthLS]

//     ph_x1 := nLS - lengthLS
//     ph_crossed := false

//     box.set_lefttop(ph_bx, ph_x1, ph_top)
//     box.set_rightbottom(ph_bx, ph_x1, ph_btm)
// else
//     ph_crossed := close > ph_top ? true : ph_crossed

//     if ph_crossed
//         box.set_right(ph_bx, ph_x1)
//     else
//         box.set_right(ph_bx, nLS+3)

// if showTop
//     //Set ph zone
//     set_zone(ph, ph_x1, ph_top, ph_btm, ph_count, ph_vol, topAreaCss)

//     //Set ph level
//     set_level(ph, ph_crossed, ph_top, ph_count, ph_vol, topCss)

//     //Set ph label
//     set_label(ph_count, ph_vol, ph_x1, ph_top, topCss, label.style_label_down)

// //-----------------------------------------------------------------------------}
// //Display pivot low levels/blocks
// //-----------------------------------------------------------------------------{
// pl = ta.pivotlow(lengthLS, lengthLS)

// //Get pl counts
// [pl_count, pl_vol] = get_counts(pl, pl_top, pl_btm)

// //Set pl area and level
// if pl and showBtm
//     pl_top := switch area
//         'Wick Extremity' => math.min(close[lengthLS], open[lengthLS])
//         'Full Range' => high[lengthLS]
//     pl_btm := low[lengthLS]

//     pl_x1 := nLS - lengthLS
//     pl_crossed := false

//     box.set_lefttop(pl_bx, pl_x1, pl_top)
//     box.set_rightbottom(pl_bx, pl_x1, pl_btm)
// else
//     pl_crossed := close < pl_btm ? true : pl_crossed

//     if pl_crossed
//         box.set_right(pl_bx, pl_x1)
//     else
//         box.set_right(pl_bx, nLS+3)

// if showBtm
//     //Set pl zone
//     set_zone(pl, pl_x1, pl_top, pl_btm, pl_count, pl_vol, btmAreaCss)

//     //Set pl level
//     set_level(pl, pl_crossed, pl_btm, pl_count, pl_vol, btmCss)

//     //Set pl labels
//     set_label(pl_count, pl_vol, pl_x1, pl_btm, btmCss, label.style_label_up)

//-----------------------------------------------------------------------------}

//==========================================================================================
// Liquidity Swings (LuxAlgo) End
//==========================================================================================

// Hull Suite
//INPUT
srcHS = input(close, title='Source')
modeSwitchHS = input.string('Hma', title='Hull Variation', options=['Hma', 'Thma', 'Ehma'])
lengthHS = input(55, title='Length(180-200 for floating S/R , 55 for swing entry)')
lengthMultHS = input(1.0, title='Length multiplier (Used to view higher timeframes with straight band)')

useHtfHS = input(false, title='Show Hull MA from X timeframe? (good for scalping)')
htfHS = input.timeframe('240', title='Higher timeframe')

switchColorHS = input(true, 'Color Hull according to trend?')
candleColHS = input(false, title='Color candles based on Hull\'s Trend?')
visualSwitchHS = input(true, title='Show as a Band?')
thicknesSwitchHS = input(1, title='Line Thickness')
transpSwitchHS = input.int(40, title='Band Transparency', step=5)

//FUNCTIONS
//HMA
HMA(_src, _length) =>
    ta.wma(2 * ta.wma(_src, _length / 2) - ta.wma(_src, _length), math.round(math.sqrt(_length)))
//EHMA
EHMA(_src, _length) =>
    ta.ema(2 * ta.ema(_src, _length / 2) - ta.ema(_src, _length), math.round(math.sqrt(_length)))
//THMA
THMA(_src, _length) =>
    ta.wma(ta.wma(_src, _length / 3) * 3 - ta.wma(_src, _length / 2) - ta.wma(_src, _length), _length)

//SWITCH
Mode(modeSwitchHS, srcHS, lenHS) =>
    modeSwitchHS == 'Hma' ? HMA(srcHS, lenHS) : modeSwitchHS == 'Ehma' ? EHMA(srcHS, lenHS) : modeSwitchHS == 'Thma' ? THMA(srcHS, lenHS / 2) : na

//OUT
_hull = Mode(modeSwitchHS, srcHS, int(lengthHS * lengthMultHS))
HULLHS = useHtfHS ? request.security(syminfo.ticker, htfHS, _hull) : _hull
MHULLHS = HULLHS[0]
SHULLHS = HULLHS[2]

//COLOR
hullColorHS = switchColorHS ? HULLHS > HULLHS[2] ? #00ff00 : #ff0000 : #ff9800

//PLOT
///< Frame
Fi1HS = plot(MHULLHS, title='MHULL', color=hullColorHS, linewidth=thicknesSwitchHS, transp=50)
Fi2HS = plot(visualSwitchHS ? SHULLHS : na, title='SHULL', color=hullColorHS, linewidth=thicknesSwitchHS, transp=50)
alertcondition(ta.crossover(MHULLHS, SHULLHS), title='Hull trending up.', message='Hull trending up.')
alertcondition(ta.crossover(SHULLHS, MHULLHS), title='Hull trending down.', message='Hull trending down.')
///< Ending Filler
fill(Fi1HS, Fi2HS, title='Band Filler', color=hullColorHS, transp=transpSwitchHS)
///BARCOLOR
barcolor(color=candleColHS ? switchColorHS ? hullColorHS : na : na)

//Trend Line
// User inputs
prd = input.int(defval=5, title=' Period for Pivot Points', minval=1, maxval=50)
max_num_of_pivots = input.int(defval=6, title=' Maximum Number of Pivots', minval=5, maxval=10)
max_lines = input.int(defval=1, title=' Maximum number of trend lines', minval=1, maxval=10)
show_lines = input.bool(defval=true, title=' Show trend lines')
show_pivots = input.bool(defval=false, title=' Show Pivot Points')
sup_line_color = input(defval = color.lime, title = "Colors", inline = "tcol")
res_line_color = input(defval = color.red, title = "", inline = "tcol")

float p_h = ta.pivothigh(high, prd, prd)
float p_l = ta.pivotlow(low, prd, prd)

plotshape(p_h and show_pivots, style=shape.triangledown, location=location.abovebar, offset=-prd, size=size.tiny)
plotshape(p_l and show_pivots, style=shape.triangleup, location=location.belowbar, offset=-prd, size=size.tiny)

// Creating array of pivots
var pivots_high = array.new_float(0)
var pivots_low = array.new_float(0)

var high_ind = array.new_int(0)
var low_ind = array.new_int(0)

if p_h
    array.push(pivots_high, p_h)
    array.push(high_ind, bar_index - prd)
    if array.size(pivots_high) > max_num_of_pivots  // limit the array size
        array.shift(pivots_high)
        array.shift(high_ind)

if p_l
    array.push(pivots_low, p_l)
    array.push(low_ind, bar_index - prd)
    if array.size(pivots_low) > max_num_of_pivots  // limit the array size
        array.shift(pivots_low)
        array.shift(low_ind)

// Create arrays to store slopes and lines
var res_lines = array.new_line()
var res_slopes = array.new_float()

len_lines = array.size(res_lines)

if (len_lines >= 1)
    for ind = 0 to len_lines - 1
        to_delete = array.pop(res_lines)
        array.pop(res_slopes)
        line.delete(to_delete)


count_slope(p_h1, p_h2, pos1, pos2) => (p_h2 - p_h1) / (pos2 - pos1)


if array.size(pivots_high) == max_num_of_pivots
    index_of_biggest_slope = 0
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_high, ind1)
            p2 = array.get(pivots_high, ind2)
            pos1 = array.get(high_ind, ind1)
            pos2 = array.get(high_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_high, ind3)
                    pos3 = array.get(high_ind, ind3)
                    if p3 > k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] > p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k


            if ok
                if array.size(res_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                    array.push(res_lines, line)
                    array.push(res_slopes, k)
                else
                    max_slope = array.max(res_slopes)
                    max_slope_ind = array.indexof(res_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k < max_slope
                        line_to_delete = array.get(res_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=res_line_color)//, extend=extend.right)
                        array.insert(res_lines, max_slope_ind, new_line)
                        array.insert(res_slopes, max_slope_ind, k)
                        array.remove(res_lines, max_slope_ind + 1)
                        array.remove(res_slopes, max_slope_ind + 1)

if not show_lines
    len_l = array.size(res_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(res_lines)
            array.pop(res_slopes)
            line.delete(to_delete)



var sup_lines = array.new_line()
var sup_slopes = array.new_float()

len_lines1 = array.size(sup_lines)

if (len_lines1 >= 1)
    for ind = 0 to len_lines1 - 1
        to_delete = array.pop(sup_lines)
        array.pop(sup_slopes)
        line.delete(to_delete)

if array.size(pivots_low) == max_num_of_pivots
    for ind1 = 0 to max_num_of_pivots - 2
        for ind2 = ind1 + 1 to max_num_of_pivots - 1
            p1 = array.get(pivots_low, ind1)
            p2 = array.get(pivots_low, ind2)
            pos1 = array.get(low_ind, ind1)
            pos2 = array.get(low_ind, ind2)
            k = count_slope(p1, p2, pos1, pos2)
            b = p1 - k * pos1

            ok = true

            // check if pivot points in the middle of two points is lower
            if ind2 - ind1 >= 1 and ok
                for ind3 = ind1 + 1 to ind2 - 1
                    p3 = array.get(pivots_low, ind3)
                    pos3 = array.get(low_ind, ind3)
                    if p3 < k * pos3 + b
                        ok := false
                        break

            pos3 = 0
            p_val = p2 + k
            if ok
                for ind = pos2 + 1 to bar_index
                    if close[bar_index - ind] < p_val
                        ok := false
                        break
                    pos3 := ind + 1
                    p_val += k

            if ok
                if array.size(sup_slopes) < max_lines
                    line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                    array.push(sup_lines, line)
                    array.push(sup_slopes, k)
                else
                    max_slope = array.min(sup_slopes)
                    max_slope_ind = array.indexof(sup_slopes, max_slope)
                    if max_lines == 1
                        max_slope_ind := 0
                    if k > max_slope
                        line_to_delete = array.get(sup_lines, max_slope_ind)
                        line.delete(line_to_delete)
                        new_line = line.new(pos1, p1, pos3, p_val, color=sup_line_color)//, extend=extend.right)
                        array.insert(sup_lines, max_slope_ind, new_line)
                        array.insert(sup_slopes, max_slope_ind, k)
                        array.remove(sup_lines, max_slope_ind + 1)
                        array.remove(sup_slopes, max_slope_ind + 1)


if not show_lines
    len_l = array.size(sup_lines)
    if (len_l >= 1)
        for ind = 0 to len_l - 1
            to_delete = array.pop(sup_lines)
            array.pop(sup_slopes)
            line.delete(to_delete)

// Super Trend
PeriodsSR = input(title='ATR Period', defval=10)
srcST = input(hl2, title='Source')
Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0)
changeATR = input(title='Change ATR Calculation Method ?', defval=true)
showsignals = input(title='Show Buy/Sell Signals ?', defval=true)
highlighting = input(title='Highlighter On/Off ?', defval=true)
atr2 = ta.sma(ta.tr, PeriodsSR)
atr = changeATR ? ta.atr(PeriodsSR) : atr2
up = srcST - Multiplier * atr
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = srcST + Multiplier * atr
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
upPlot = plot(trend == 1 ? up : na, title='Up Trend', style=plot.style_linebr, linewidth=2, color=color.new(color.green, 0))
buySignal = trend == 1 and trend[1] == -1
plotshape(buySignal ? up : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.green, 0))
plotshape(buySignal and showsignals ? up : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=color.new(color.green, 0), textcolor=color.new(color.white, 0))
dnPlot = plot(trend == 1 ? na : dn, title='Down Trend', style=plot.style_linebr, linewidth=2, color=color.new(color.red, 0))
sellSignal = trend == -1 and trend[1] == 1
plotshape(sellSignal ? dn : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.red, 0))
plotshape(sellSignal and showsignals ? dn : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=color.new(color.red, 0), textcolor=color.new(color.white, 0))
mPlot = plot(ohlc4, title='', style=plot.style_circles, linewidth=0)
longFillColor = highlighting ? trend == 1 ? color.green : color.white : color.white
shortFillColor = highlighting ? trend == -1 ? color.red : color.white : color.white
fill(mPlot, upPlot, title='UpTrend Highligter', color=longFillColor, transp=90)
fill(mPlot, dnPlot, title='DownTrend Highligter', color=shortFillColor, transp=90)
alertcondition(buySignal, title='SuperTrend Buy', message='SuperTrend Buy!')
alertcondition(sellSignal, title='SuperTrend Sell', message='SuperTrend Sell!')
changeCond = trend != trend[1]
alertcondition(changeCond, title='SuperTrend Direction Change', message='SuperTrend has changed direction!')
