// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// This script is largely based on "parallel pivot lines" by LuxAlgo and Trading Views in built auto fib retracememnt.

// © veryfid
//@version=5
indicator('Kagebunshin Support and Resistance', "Kagebunshin S&R", overlay=true, max_bars_back=5000)

g_sl = 'Swing level labels'
length = input.int(30, 'Pivot Length', group='Basic Settings', tooltip='Pivot length. Use higher values for having lines connected to more significant pivots')
lookback = input.int(3, minval=1, title='Bars to lookback', group='Basic Settings', tooltip='Number of lines connecting a pivot high/low to display')
Slope = input.float(1., minval=-1, maxval=1, step=0.1, group='Basic Settings', tooltip='Allows to multiply the linear regression slope by a number within -1 and 1')
extendL = input.bool(false, 'Extend Horizontal lines Left?', group='Basic Settings')
var extend = extend.right
if extendL
    extend := extend.both
    extend
extendL2 = input.bool(false, 'Extend Parallel lines Left?', group='Basic Settings')
var extend2 = extend.right
if extendL2
    extend2 := extend.both
    extend2

showpar = input.bool(true, 'Show Parallel Lines?', group='Show Lines')
showhor = input.bool(true, 'Show horizontal Lines?', group='Show Lines')
showsma = input.bool(false, 'Show SMA 200 Line?', group='Show Lines')
hidefib = input.bool(false, 'Hide Fibonacci Lines?', group='Show Lines')
showfib = input.bool(false, 'Show Fib Trend Line?', group='Show Lines')
showall = input.bool(false, 'Show All Fibonacci Lines?', group='Show Lines')
//Style
ph_col = input.color(color.white, 'Parallel | High Lines Color', group='Line Colors', inline='col1')
pl_col = input.color(color.white, 'Low Lines Color', group='Line Colors', inline='col1')
ph_col2 = input.color(color.red, 'horizontal | High Lines Color', group='Line Colors', inline='col2')
pl_col2 = input.color(color.green, 'Low Lines Color', group='Line Colors', inline='col2')

devTooltip = 'Deviation is a multiplier that affects how much the price should deviate from the previous pivot in order for the bar to become a new pivot.'
depthTooltip = 'The minimum number of bars that will be taken into account when calculating the indicator.'
// pivots threshold
threshold_multiplier = input.float(title='Deviation', defval=3, minval=0, tooltip=devTooltip, group='Fibonacci Auto Retracement')
dev_threshold = ta.atr(10) / close * 100 * threshold_multiplier
depth = input.int(title='Depth', defval=50, minval=1, tooltip=depthTooltip, group='Fibonacci Auto Retracement')
reverse = input.bool(false, 'Reverse', group='Fibonacci Auto Retracement')
var extendLeft = input.bool(false, 'Extend Left    |    Extend Right', inline='Extend Lines', group='Fibonacci Auto Retracement')
var extendRight = input.bool(true, '', inline='Extend Lines', group='Fibonacci Auto Retracement')
var extending = extend.none
if extendLeft and extendRight
    extending := extend.both
    extending
if extendLeft and not extendRight
    extending := extend.left
    extending
if not extendLeft and extendRight
    extending := extend.right
    extending
prices = input.bool(true, 'Show Prices', group='Fibonacci Auto Retracement')
levels = input.bool(true, 'Show Levels', inline='Levels', group='Fibonacci Auto Retracement')
levelsFormat = input.string('Values', '', options=['Values', 'Percent'], inline='Levels', group='Fibonacci Auto Retracement')
labelsPosition = input.string('Right', 'Labels Position', options=['Left', 'Right'], group='Fibonacci Auto Retracement')

//──────────────────────────────────────────────────────────────────────────────
Sma(src, p) =>
    a = ta.cum(src)
    (a - a[math.max(p, 0)]) / math.max(p, 0)
Variance(src, p) =>
    p == 1 ? 0 : Sma(src * src, p) - math.pow(Sma(src, p), 2)
Covariance(x, y, p) =>
    Sma(x * y, p) - Sma(x, p) * Sma(y, p)
//──────────────────────────────────────────────────────────────────────────────
n = bar_index
ph = ta.pivothigh(length, length)
pl = ta.pivotlow(length, length)

//──────────────────────────────────────────────────────────────────────────────
varip ph_array = array.new_float(0)
varip pl_array = array.new_float(0)
varip ph_n_array = array.new_int(0)
varip pl_n_array = array.new_int(0)
if ph
    array.insert(ph_array, 0, ph)
    array.insert(ph_n_array, 0, n)
if pl
    array.insert(pl_array, 0, pl)
    array.insert(pl_n_array, 0, n)
//──────────────────────────────────────────────────────────────────────────────
val_ph = ta.valuewhen(ph, n - length, lookback - 1)
val_pl = ta.valuewhen(pl, n - length, lookback - 1)
val = math.min(val_ph, val_pl)
k = n - val > 0 ? n - val : 2
slope = Covariance(close, n, k) / Variance(n, k) * Slope
var line ph_l = na
var line pl_l = na
var line ph_l2 = na
var line pl_l2 = na

if barstate.islast and showpar
    for i = 0 to lookback - 1 by 1
        ph_y2 = array.get(ph_array, i)
        ph_x1 = array.get(ph_n_array, i) - length
        pl_y2 = array.get(pl_array, i)
        pl_x1 = array.get(pl_n_array, i) - length
        ph_l2 := line.new(ph_x1, ph_y2, ph_x1 + 1, ph_y2 + slope, extend=extend2, color=color.new(ph_col2, 70))
        pl_l2 := line.new(pl_x1, pl_y2, pl_x1 + 1, pl_y2 + slope, extend=extend2, color=color.new(pl_col2, 70))
        pl_l2

if barstate.islast and showhor
    for i = 0 to lookback - 1 by 1
        ph_y2 = array.get(ph_array, i)
        ph_x1 = array.get(ph_n_array, i) - length
        pl_y2 = array.get(pl_array, i)
        pl_x1 = array.get(pl_n_array, i) - length
        ph_l := line.new(ph_x1, ph_y2, ph_x1 + 1, ph_y2, extend=extend, color=ph_col)
        pl_l := line.new(pl_x1, pl_y2, pl_x1 + 1, pl_y2, extend=extend, color=pl_col)
        pl_l
//Fibonacci

var line lineLast = na
var int iLast = 0
var int iPrev = 0
var float pLast = 0
var isHighLast = false  // otherwise the last pivot is a low pivot

pivots(src, length, isHigh) =>
    l2 = length * 2
    c = nz(src[length])
    ok = true
    for i = 0 to l2 by 1
        if isHigh and src[i] > c
            ok := false
            ok

        if not isHigh and src[i] < c
            ok := false
            ok
    if ok
        [bar_index[length], c]
    else
        [int(na), float(na)]
[iH, pH] = pivots(high, depth / 2, true)
[iL, pL] = pivots(low, depth / 2, false)

calc_dev(base_price, price) =>
    100 * (price - base_price) / price

pivotFound(dev, isHigh, index, price) =>
    if isHighLast == isHigh and not na(lineLast)
        // same direction
        if isHighLast ? price > pLast : price < pLast
            line.set_xy2(lineLast, index, price)
            [lineLast, isHighLast]
        else
            [line(na), bool(na)]
    else
        // reverse the direction (or create the very first line)
        if math.abs(dev) > dev_threshold
            // price move is significant
            id = line.new(iLast, pLast, index, price, color=color.new(color.gray, showfib ? 70 : 100), width=1, style=line.style_dashed)
            [id, isHigh]
        else
            [line(na), bool(na)]

if not na(iH)
    dev = calc_dev(pLast, pH)
    [id, isHigh] = pivotFound(dev, true, iH, pH)
    if not na(id)
        if id != lineLast
            line.delete(lineLast)
        lineLast := id
        isHighLast := isHigh
        iPrev := iLast
        iLast := iH
        pLast := pH
        pLast
else
    if not na(iL)
        dev = calc_dev(pLast, pL)
        [id, isHigh] = pivotFound(dev, false, iL, pL)
        if not na(id)
            if id != lineLast
                line.delete(lineLast)
            lineLast := id
            isHighLast := isHigh
            iPrev := iLast
            iLast := iL
            pLast := pL
            pLast

_draw_line(price, col) =>
    var id = line.new(iLast, price, bar_index, price, color=col, width=1, extend=extend.none, color=col, width=1, style=line.style_dashed)  // No automatic extension
    if not na(lineLast)
        line.set_xy1(id, line.get_x1(lineLast), price)
        line.set_xy2(id, line.get_x2(lineLast) + 100, price)  // Extend only 10 bars to the right


_draw_label(price, txt, txtColor) =>
    x = labelsPosition == 'Left' ? line.get_x1(lineLast) : not extendRight ? line.get_x2(lineLast) : bar_index
    labelStyle = labelsPosition == 'Left' ? label.style_label_right : label.style_label_left
    align = labelsPosition == 'Left' ? text.align_right : text.align_left
    labelsAlignStrLeft = txt + '\n ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏ \n'
    labelsAlignStrRight = '       ' + txt + '\n ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏  ‏ \n'
    labelsAlignStr = labelsPosition == 'Left' ? labelsAlignStrLeft : labelsAlignStrRight
    var id = label.new(x=x, y=price, text=labelsAlignStr, textcolor=txtColor, style=labelStyle, textalign=align, color=#00000000)
    label.set_xy(id, x, price)
    label.set_text(id, labelsAlignStr)
    label.set_textcolor(id, txtColor)

_wrap(txt) =>
    '(' + str.tostring(txt, '#.##') + ')'

_label_txt(level, price) =>
    l = levelsFormat == 'Values' ? str.tostring(level) : str.tostring(level * 100) + '%'
    (levels ? l : '') + (prices ? _wrap(price) : '')

_crossing_level(sr, r) =>
    r > sr and r < sr[1] or r < sr and r > sr[1]

startPrice = reverse ? line.get_y1(lineLast) : pLast
endPrice = reverse ? pLast : line.get_y1(lineLast)

iHL = startPrice > endPrice
diff = (iHL ? -1 : 1) * math.abs(startPrice - endPrice)

processLevel(show, value, colorL) =>
    float m = value
    r = startPrice + diff * m
    if show
        _draw_line(r, colorL)
        _draw_label(r, _label_txt(m, r), colorL)
        // Check for specific Fibonacci levels and assign the corresponding "Kagebunshin" text
        var string kagebunshinTxt = ""
        if (m == 0.5)
            kagebunshinTxt := "Kagebunshin 1"
        else if (m == 0.618)
            kagebunshinTxt := "Kagebunshin 2"
        else if (m == 0.786)
            kagebunshinTxt := "Kagebunshin 3"
        else if (m == 1)
            kagebunshinTxt := "Kagebunshin 4"
        else if (m == 0.382)
            kagebunshinTxt := "Kagebunshin 2"
        else if (m == 0.236)
            kagebunshinTxt := "Kagebunshin 3"
        else if (m == 0)
            kagebunshinTxt := "Kagebunshin 4"

        // Append the "Kagebunshin" label to the Fibonacci level only if it matches
        if (kagebunshinTxt != "")
            _draw_label(r, _label_txt(m, r) + " " + kagebunshinTxt, colorL)
        else
            _draw_label(r, _label_txt(m, r), colorL)
        if _crossing_level(close, r)
            alert('Autofib: ' + syminfo.ticker + ' crossing level ' + str.tostring(value))

show_0_236 = input.bool(true, '', inline='Level0', group='Show Lines')
if showall
    show_0_236 := true
    show_0_236
if hidefib
    show_0_236 := false
    show_0_236
value_0_236 = input.float(0.236, '', inline='Level0', group='Show Lines')
color_0_236 = input.color(#81c784, '', inline='Level0', group='Show Lines')
processLevel(show_0_236, value_0_236, color_0_236)

show_0_382 = input.bool(true, '', inline='Level1', group='Show Lines')
if showall
    show_0_382 := true
    show_0_382
if hidefib
    show_0_382 := false
    show_0_382
value_0_382 = input.float(0.382, '', inline='Level1', group='Show Lines')
color_0_382 = input.color(#81c784, '', inline='Level1', group='Show Lines')
processLevel(show_0_382, value_0_382, color_0_382)

show_0_618 = input.bool(true, '', inline='Level2', group='Show Lines')
if showall
    show_0_618 := true
    show_0_618
if hidefib
    show_0_618 := false
    show_0_618
value_0_618 = input.float(0.618, '', inline='Level2', group='Show Lines')
color_0_618 = input.color(#f44336, '', inline='Level2', group='Show Lines')
processLevel(show_0_618, value_0_618, color_0_618)

show_0_786 = input.bool(true, '', inline='Level3', group='Show Lines')
if showall
    show_0_786 := true
    show_0_786
if hidefib
    show_0_786 := false
    show_0_786
value_0_786 = input.float(0.786, '', inline='Level3', group='Show Lines')
color_0_786 = input.color(#f44336, '', inline='Level3', group='Show Lines')
processLevel(show_0_786, value_0_786, color_0_786)

show_0 = input.bool(true, '', inline='Level4', group='Show Lines')
if showall
    show_0 := true
    show_0
if hidefib
    show_0 := false
    show_0
value_0 = input.int(0, '', inline='Level4', group='Show Lines')
color_0 = input.color(color.gray, '', inline='Level4', group='Show Lines')
processLevel(show_0, value_0, color_0)

show_0_5 = input.bool(true, '', inline='Level5', group='Show Lines')
if showall
    show_0_5 := true
    show_0_5
if hidefib
    show_0_5 := false
    show_0_5
value_0_5 = input.float(0.5, '', inline='Level5', group='Show Lines')
color_0_5 = input.color(#009688, '', inline='Level5', group='Show Lines')
processLevel(show_0_5, value_0_5, color_0_5)

show_1 = input.bool(true, '', inline='Level0', group='Show Lines')
if showall
    show_1 := true
    show_1
if hidefib
    show_1 := false
    show_1
value_1 = input.int(1, '', inline='Level0', group='Show Lines')
color_1 = input.color(color.gray, '', inline='Level0', group='Show Lines')
processLevel(show_1, value_1, color_1)

show_1_272 = input.bool(false, '', inline='Level1', group='Show Lines')
if showall
    show_1_272 := true
    show_1_272
if hidefib
    show_1_272 := false
    show_1_272
value_1_272 = input.float(1.272, '', inline='Level1', group='Show Lines')
color_1_272 = input.color(color.gray, '', inline='Level1', group='Show Lines')
processLevel(show_1_272, value_1_272, color_1_272)

show_1_414 = input.bool(false, '', inline='Level2', group='Show Lines')
if showall
    show_1_414 := true
    show_1_414
if hidefib
    show_1_414 := false
    show_1_414
value_1_414 = input.float(1.414, '', inline='Level2', group='Show Lines')
color_1_414 = input.color(color.gray, '', inline='Level2', group='Show Lines')
processLevel(show_1_414, value_1_414, color_1_414)

show_1_618 = input.bool(false, '', inline='Level3', group='Show Lines')
if showall
    show_1_618 := true
    show_1_618
if hidefib
    show_1_618 := false
    show_1_618
value_1_618 = input.float(1.618, '', inline='Level3', group='Show Lines')
color_1_618 = input.color(color.green, '', inline='Level3', group='Show Lines')
processLevel(show_1_618, value_1_618, color_1_618)

show_1_786 = input.bool(false, '', inline='Level4', group='Show Lines')
if showall
    show_1_786 := true
    show_1_786
if hidefib
    show_1_786 := false
    show_1_786
value_1_786 = input.float(1.786, '', inline='Level4', group='Show Lines')
color_1_786 = input.color(color.green, '', inline='Level4', group='Show Lines')
processLevel(show_1_786, value_1_786, color_1_786)

show_2 = input.bool(false, '', inline='Level5', group='Show Lines')
if showall
    show_2 := true
    show_2
if hidefib
    show_2 := false
    show_2
value_2 = input.int(2, '', inline='Level5', group='Show Lines')
color_2 = input.color(color.red, '', inline='Level5', group='Show Lines')
processLevel(show_2, value_2, color_2)

ema200 = ta.ema(close, 200)
sma200 = ta.sma(close, 200)
sma200plot = plot(showsma ? sma200 : na, color=ema200 > sma200 ? color.white : color.yellow, linewidth=2, title='SMA(200)', transp=0)

//Swing level labels
showSwing = input.bool(true, 'Show Swing Points', tooltip='Show or hide HH, LH, HL, LL', group='Swing level labels')
// Constants
color CLEAR = color.rgb(0,0,0,100)

// Inputs
swingSize = input.int(20, 'Swing Length', tooltip='The number of left and right bars checked when searching for a swing point. Higher value = less swing points plotted and lower value = more swing points plotted.')

// Calculations

// Finding high and low pivots
pivHi = ta.pivothigh(high, swingSize, swingSize)
pivLo = ta.pivotlow(low, swingSize, swingSize)

// Tracking the previous swing levels to determine HH, LH, HL, LL
var float prevHigh = na
var float prevLow = na

bool hh = false
bool lh = false
bool hl = false
bool ll = false

if not na(pivHi)
    if pivHi >= prevHigh
        hh := true
    else
        lh := true
    prevHigh := pivHi

if not na(pivLo)
    if pivLo >= prevLow
        hl := true
    else
        ll := true
    prevLow := pivLo

// Visual Output

// Swing level labels, only display if showSwing is enabled
if showSwing
    if hh
        label.new(bar_index - swingSize, pivHi, 'HH', color=CLEAR, style=label.style_label_down, textcolor=chart.fg_color)

    if lh
        label.new(bar_index - swingSize, pivHi, 'LH', color=CLEAR, style=label.style_label_down, textcolor=chart.fg_color)

    if hl
        label.new(bar_index - swingSize, pivLo, 'HL', color=CLEAR, style=label.style_label_up, textcolor=chart.fg_color)

    if ll
        label.new(bar_index - swingSize, pivLo, 'LL', color=CLEAR, style=label.style_label_up, textcolor=chart.fg_color)

