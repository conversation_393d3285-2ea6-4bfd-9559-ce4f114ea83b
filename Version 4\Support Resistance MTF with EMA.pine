//@version=4
study("Support Resistance MTF with EMA", overlay=true, max_bars_back = 4900)

src = input(defval = 'High/Low', title = "Source", options = ['High/Low', 'Close/Open'])
TimeframeU = input(defval = 'Auto', title ="Higher Time Frame", options = ['Auto', '15', '30', '60', '120', '180', '240', '720', 'D', 'W', '2W', 'M', '3M', '6M', '12M'])
prd = input(defval = 10, title = "Period for Highest/Lowest Bars", minval = 1)
resistancecol = input(defval = color.red, title = "Resistance Color")
supportcol = input(defval = color.lime, title = "Support Color")
Transp = input(defval = 80, title = "Transparency for Zones", minval = 0, maxval = 100)
extendlines = input(defval = false, title = "Extend Lines")

Timeframe = timeframe.period
if TimeframeU == 'Auto'
    Timeframe := timeframe.period == '1' ? '15' :
           timeframe.period == '3' ? '15' :
           timeframe.period == '5' ? '60' :
           timeframe.period == '15' ? '60' :
           timeframe.period == '30' ? '120' :
           timeframe.period == '45' ? '120' :
           timeframe.period == '60' ? '240' :
           timeframe.period == '120' ? 'D' :
           timeframe.period == '180' ? 'D' :
           timeframe.period == '240' ? 'D' :
           timeframe.period == 'D' ? 'W' :
           timeframe.period == 'W' ? 'M' :
           '12M'
else
    Timeframe := TimeframeU

var float hc = na
var float lc = na
srch = src == 'High/Low' ? high : max(close, open)
srcl = src == 'High/Low' ? low : min(close, open)
hc := highestbars(srch, prd) == 0 ? srch : hc
lc := lowestbars(srcl, prd) == 0 ? srcl : lc

hcp = plot(hc, color = hc == hc[1] ? resistancecol : na)
lcp = plot(lc, color = lc == lc[1] ? supportcol : na)

bool newbar = change(time(Timeframe)) != 0
var float htfh = na
var float htfl = na
if newbar
    htfh := srch
    htfl := srcl
else
    htfh := max(htfh, srch)
    htfl := min(htfl, srcl)

highestbar(src, len)=>
    ll = 1
    if len > 1
        for x = 0 to 4000
            if na(newbar[x]) or na(close[x + 1])
                break
            if newbar[x]
                if src <= src[x + 1]
                    break
                ll := ll + 1
                if ll >= len
                    break
    ret = ll >= len

lowestbar(src, len)=>
    ll = 1
    if len > 1
        for x = 0 to 4000
            if na(newbar[x]) or na(close[x + 1])
                break
            if newbar[x]
                if src >= src[x + 1]
                    break
                ll := ll + 1
                if ll >= len
                    break
    ret = ll >= len

var float hh = 0
var float ll = 0
bool hbar = highestbar(htfh, prd)
bool lbar = lowestbar(htfl, prd)
hh := hbar ? htfh : hh
ll := lbar ? htfl : ll

hhp = plot(hh, color = hh == hh[1] ? resistancecol : na)
llp = plot(ll, color = ll == ll[1] ? supportcol : na)

fill(hhp, hcp, color = hc == hc[1] and hh == hh[1] ? color.new(resistancecol, Transp) : na)
fill(llp, lcp, color = lc == lc[1] and ll == ll[1] ? color.new(supportcol, Transp) : na)

// EMA 20
length20 = input(20, title="EMA 20 Period")
ema20 = ema(close, length20)
ema20Color = ema20[1] > low ? color.red : ema20[1] < high ? color.green : na
plot(ema20, color=ema20Color, title="EMA 20")

// EMA 50
length50 = input(50, title="EMA 50 Period")
ema50 = ema(close, length50)
ema50Color = ema50[1] > low ? color.red : ema50[1] < high ? color.green : na
plot(ema50, color=ema50Color, title="EMA 50")

// EMA 100
length100 = input(100, title="EMA 100 Period")
ema100 = ema(close, length100)
ema100Color = ema100[1] > low ? color.red : ema100[1] < high ? color.green : na
plot(ema100, color=ema100Color, title="EMA 100")

// EMA 200
length200 = input(200, title="EMA 200 Period")
ema200 = ema(close, length200)
ema200Color = ema200[1] > low ? color.red : ema200[1] < high ? color.green : na
plot(ema200, color=ema200Color, title="EMA 200")

// extend lines
if extendlines
    var line s1 = na
    var line s2 = na
    var line r1 = na
    var line r2 = na
    line.delete(s1)
    line.delete(s2)
    line.delete(r1)
    line.delete(r2)
    s1 := line.new(bar_index, lc, bar_index - 1, lc, color = supportcol, extend = extend.left)
    s2 := line.new(bar_index, ll, bar_index - 1, ll, color = supportcol, extend = extend.left)
    r1 := line.new(bar_index, hc, bar_index - 1, hc, color = resistancecol, extend = extend.left)
    r2 := line.new(bar_index, hh, bar_index - 1, hh, color = resistancecol, extend = extend.left)
