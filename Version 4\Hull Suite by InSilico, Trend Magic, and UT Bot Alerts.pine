//@version=4
//Basic Hull Ma Pack tinkered by InSilico
study("Hull Suite by InSilico, Trend Magic, and UT Bot Alerts", overlay=true)

//INPUT
src = input(close, title="Source")
modeSwitch = input("Hma", title="Hull Variation", options=["Hma", "Thma", "Ehma"])
length = input(55, title="Length(180-200 for floating S/R , 55 for swing entry)")
lengthMult = input(1.0, title="Length multiplier (Used to view higher timeframes with straight band)")

useHtf = input(false, title="Show Hull MA from X timeframe? (good for scalping)")
htf = input("240", title="Higher timeframe", type=input.resolution)

switchColor = input(true, "Color Hull according to trend?")
candleCol = input(false,title="Color candles based on Hull's Trend?")
visualSwitch  = input(true, title="Show as a Band?")
thicknesSwitch = input(1, title="Line Thickness")
transpSwitch = input(40, title="Band Transparency",step=5)

//FUNCTIONS
//HMA
HMA(_src, _length) =>  wma(2 * wma(_src, _length / 2) - wma(_src, _length), round(sqrt(_length)))
//EHMA
EHMA(_src, _length) =>  ema(2 * ema(_src, _length / 2) - ema(_src, _length), round(sqrt(_length)))
//THMA
THMA(_src, _length) =>  wma(wma(_src,_length / 3) * 3 - wma(_src, _length / 2) - wma(_src, _length), _length)

//SWITCH
Mode(modeSwitch, src, len) =>
      modeSwitch == "Hma"  ? HMA(src, len) :
      modeSwitch == "Ehma" ? EHMA(src, len) :
      modeSwitch == "Thma" ? THMA(src, len/2) : na

//OUT
_hull = Mode(modeSwitch, src, int(length * lengthMult))
HULL = useHtf ? security(syminfo.ticker, htf, _hull) : _hull
MHULL = HULL[0]
SHULL = HULL[2]

//COLOR
hullColor = switchColor ? (HULL > HULL[2] ? #00ff00 : #ff0000) : #ff9800

//PLOT
///< Frame
Fi1 = plot(MHULL, title="MHULL", color=hullColor, linewidth=thicknesSwitch, transp=50)
Fi2 = plot(visualSwitch ? SHULL : na, title="SHULL", color=hullColor, linewidth=thicknesSwitch, transp=50)
alertcondition(crossover(MHULL, SHULL), title="Hull trending up.", message="Hull trending up.")
alertcondition(crossover(SHULL, MHULL), title="Hull trending down.", message="Hull trending down.")
///< Ending Filler
fill(Fi1, Fi2, title="Band Filler", color=hullColor, transp=transpSwitch)
///BARCOLOR
barcolor(color = candleCol ? (switchColor ? hullColor : na) : na)

// UT BOT ALERT
// Inputs
a = input(1,     title = "Key Vaule. 'This changes the sensitivity'")
c = input(10,    title = "ATR Period")
h = input(false, title = "Signals from Heikin Ashi Candles")

xATR  = atr(c)
nLoss = a * xATR

srcUt = h ? security(heikinashi(syminfo.tickerid), timeframe.period, close, lookahead = false) : close

xATRTrailingStop = 0.0
xATRTrailingStop := iff(srcUt > nz(xATRTrailingStop[1], 0) and srcUt[1] > nz(xATRTrailingStop[1], 0), max(nz(xATRTrailingStop[1]), srcUt - nLoss),
   iff(srcUt < nz(xATRTrailingStop[1], 0) and srcUt[1] < nz(xATRTrailingStop[1], 0), min(nz(xATRTrailingStop[1]), srcUt + nLoss),
   iff(srcUt > nz(xATRTrailingStop[1], 0), srcUt - nLoss, srcUt + nLoss)))

pos = 0
pos :=	iff(srcUt[1] < nz(xATRTrailingStop[1], 0) and srcUt > nz(xATRTrailingStop[1], 0), 1,
   iff(srcUt[1] > nz(xATRTrailingStop[1], 0) and srcUt < nz(xATRTrailingStop[1], 0), -1, nz(pos[1], 0)))

xcolor = pos == -1 ? color.red: pos == 1 ? color.green : color.blue

ema   = ema(srcUt,1)
above = crossover(ema, xATRTrailingStop)
below = crossover(xATRTrailingStop, ema)

buy  = srcUt > xATRTrailingStop and above
sell = srcUt < xATRTrailingStop and below

barbuy  = srcUt > xATRTrailingStop
barsell = srcUt < xATRTrailingStop

plotshape(buy,  title = "Buy",  text = 'Buy',  style = shape.labelup,   location = location.belowbar, color= color.green, textcolor = color.white, transp = 0, size = size.tiny)
plotshape(sell, title = "Sell", text = 'Sell', style = shape.labeldown, location = location.abovebar, color= color.red,   textcolor = color.white, transp = 0, size = size.tiny)

barcolor(barbuy  ? color.green : na, title = "Bar Color UT Bot Buy")
barcolor(barsell ? color.red   : na, title = "Bar Color UT Bot Buy")

alertcondition(buy,  "UT Long",  "UT Long")
alertcondition(sell, "UT Short", "UT Short")

// Trend Magic
period=input(20,"CCI period")
coeff=input(1,"ATR Multiplier")
AP=input(5,"ATR Period")
ATR=sma(tr,AP)
srcTM=input(close)
upT=low-ATR*coeff
downT=high+ATR*coeff
MagicTrend=0.0
MagicTrend := cci(srcTM,period)>=0 ? (upT<nz(MagicTrend[1]) ? nz(MagicTrend[1]) : upT) : (downT>nz(MagicTrend[1]) ? nz(MagicTrend[1]) : downT)
color1= cci(srcTM,period)>=0 ? #0022FC : #FC0400
plot(MagicTrend, title="MagicTrend", color=color1, linewidth=3)
alertcondition(cross(close, MagicTrend), title="Cross Alert", message="Price - MagicTrend Crossing!")
alertcondition(crossover(low, MagicTrend), title="CrossOver Alarm", message="BUY SIGNAL!")
alertcondition(crossunder(high, MagicTrend), title="CrossUnder Alarm", message="SELL SIGNAL!")
