// This source code is subject to the terms of the Mozilla Public License 2.0 at mozilla.org/MPL/2.0/

//@version=5
//AK MACD BB
//created by Algokid , February 24,2015
//modded by <PERSON>, July 6, 2016
color_grey_light = #aa9e9e
color_grey = #6c6160
color_grey_dark = #484141

color_red_light = #e57373
color_red = #f44336
color_red_dark = #d32f2f

color_blue_light = #64b5f6
color_blue = #2196f3
color_blue_dark = #1976d2

color_green_light = #81c784
color_green = #4caf50
color_green_dark = #388e3c

color_orange_light = #ffb74d
color_orange = #ff9800
color_orange_dark = #f57c00

color_lime_light = #dce775
color_lime = #cddc39
color_lime_dark = #afb42b

indicator("AK MACD BB v 1.00")

length = input.int(10, minval=1, title="BB Periods")
dev = input.float(1, minval=0.0001, title="Deviations")
bar_color = input(false, "Bar colors")
//MACD
fastLength = input.int(12, minval=1)
slowLength=input.int(26,minval=1)
signalLength=input.int(9,minval=1)
fastMA = ta.ema(close, fastLength)
slowMA = ta.ema(close, slowLength)
macd = fastMA - slowMA

//BollingerBands

Std = ta.stdev(macd, length)
Upper = (Std * dev + (ta.sma(macd, length)))
Lower = ((ta.sma(macd, length)) - (Std * dev))

Band1 = plot(Upper, color=color_grey_light, style=plot.style_line, linewidth=2,title="Upper Band")
Band2 = plot(Lower, color=color_grey_light, style=plot.style_line, linewidth=2,title="lower Band")
fill(Band1, Band2, color=color_blue_light, transp=75,title="Fill")

mc = macd >= Upper ? color_green: macd <= Lower ? color_red : color_orange

// Indicator

plot(macd, color=mc, style=plot.style_line,linewidth = 3)
zeroline = 0
plot(zeroline,color=color_orange,linewidth= 2,title="Zeroline")

//buy
barcolor(bar_color and macd > Upper ? color_lime:na)
//short
barcolor(bar_color and macd < Lower ? color_orange:na)