// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © GoldenEntry

//@version=6
indicator("Trend Targets [Golden Entry]", "Golden Entry - Trend Targets", overlay = true)

// Trend settings
st_factor = input.float(5, title="Supertrend Factor", minval=1, step=0.5, group="Trend Settings",
     tooltip="Multiplier for the ATR to determine Supertrend bands width. Higher values create wider bands and fewer signals.")
st_atr_period = input.int(90, title="Supertrend ATR Period", minval=1, group="Trend Settings",
     tooltip="Number of bars used to calculate the ATR for Supertrend. Longer periods create smoother, less reactive bands.")
wma_length = input.int(50, title="WMA Length", minval=1, group="Trend Settings",
     tooltip="Length of the Weighted Moving Average applied to the SuperTrend. Higher values create smoother, less reactive lines.")
ema_length = input.int(14, title="EMA Length", minval=1, group="Trend Settings",
     tooltip="Length of the Exponential Moving Average applied to the WMA. Controls the final smoothness of the trend line.")

//Continuation settings
cont_factor = input.int(3, title="Confirmation count", minval=1, group="Rejection Settings",
     tooltip="Number of consecutive bars that must consolidate at the trend line before a rejection signal is generated. Higher values require more bars to confirm a trend.")

// Manual Entry Settings
manual_entry_enabled = input.bool(false, title="Enable Manual Entry", group="Manual Entry",
     tooltip="Enable manual entry mode. When enabled, you can set custom entry prices for long/short positions.")
manual_long_entry = input.float(0, title="Long Entry Price", minval=0, step=0.01, group="Manual Entry",
     tooltip="Manual entry price for long position. Set to 0 to disable long entry.")
manual_short_entry = input.float(0, title="Short Entry Price", minval=0, step=0.01, group="Manual Entry",
     tooltip="Manual entry price for short position. Set to 0 to disable short entry.")

// Volatility settings
shw_TP1 = input.bool(true, title="Show Take Profit Levels", group="Targets",
     tooltip="Toggle visibility of take profit target levels on the chart.")
atr_period = input.int(14, title="Volatility (ATR) period", minval=1, group="Targets",
     tooltip="Number of bars used to calculate the Average True Range for position sizing and targets.")
sl_multiplier = input.float(5, title="Stop Loss ATR Multiplier", minval=0.1, step=0.1, group="Targets",
     tooltip="Multiplier applied to ATR to determine stop loss distance from entry. Higher values place stops further away.")
// New input for Entry 2 multiplier
entry2_multiplier = input.float(0.5, title="Entry 2 Multiplier", minval=0.1, step=0.1, tooltip="Multiple of SL distance for a secondary entry target.", group="Targets")
tp1_multiplier = input.float(0.5, title="TP1 Multiplier", minval=0.1, step=0.1, tooltip="Multiple of SL distance for first take profit target.", group="Targets")
tp2_multiplier = input.float(1.0, title="TP2 Multiplier", minval=0.1, step=0.1, tooltip="Multiple of SL distance for second take profit target.", group="Targets")
tp3_multiplier = input.float(1.5, title="TP3 Multiplier", minval=0.1, step=0.1, tooltip="Multiple of SL distance for third take profit target.", group="Targets")

volatility = ta.atr(atr_period)
// Appearance settings
green = input.color(#00ffbb, title="Bullish Color", tooltip="Color used for bullishness", group="Appearance")
red = input.color(#ff1100, title="Bearish Color", tooltip="Color used for bearishness", group="Appearance")


pine_supertrend(factor, atrPeriod) =>
    src = hl2
    atr = ta.atr(atrPeriod)
    upperBand = src + factor * atr
    lowerBand = src - factor * atr
    prevLowerBand = nz(lowerBand[1])
    prevUpperBand = nz(upperBand[1])

    lowerBand := lowerBand > prevLowerBand or close[1] < prevLowerBand ? lowerBand : prevLowerBand
    upperBand := upperBand < prevUpperBand or close[1] > prevUpperBand ? upperBand : prevUpperBand

    [lowerBand, upperBand]

[lwr, upr] = pine_supertrend(st_factor, st_atr_period)
tL = ta.ema(ta.wma(math.avg(lwr, upr), wma_length), ema_length)

var trend = 0
if ta.crossover(tL, tL[1])
    trend := 1
if ta.crossunder(tL, tL[1])
    trend := -1

var rejcount = 0

bullishrej = trend == 1 and high > tL and low < tL
bearishrej = trend == -1 and high > tL and low < tL

if (bullishrej or bearishrej)
    rejcount += 1

if ta.cross(trend, 0) or (not (bullishrej or bearishrej) and rejcount > 0)
    rejcount := 0

plotchar((rejcount > cont_factor and trend == 1) ? tL  : na, "Bullish Rejection", "▲", location.belowbar, green, size = size.tiny)
plotchar((rejcount > cont_factor and trend == -1) ? tL  : na, "Bearish Rejection", "▼", location.abovebar, red, size = size.tiny)

plot(tL, "Baseline", color=trend == 1 ? color.new(green, 50) : color.new(red, 50))

// Bar color default has been commented out to disable it.
// barcolor(trend == 1 ? color.new(green, 50) : color.new(red, 50))

// Only show trend change signals when manual entry is disabled
plotshape(not manual_entry_enabled and ta.crossover(tL, tL[1]) ? tL : na, title="Bullish Trend Change", text="Buy", style=shape.labelup, location=location.belowbar, size=size.tiny, color=color.new(color.teal,transp = 50), textcolor=color.white)
plotshape(not manual_entry_enabled and ta.crossunder(tL, tL[1]) ? tL : na, title="Bearish Trend Change", text="Sell", style=shape.labeldown, location=location.abovebar, size=size.tiny, color=color.new(color.red,transp = 50), textcolor=color.white)

// Show manual entry points when enabled
plotshape(manual_entry_enabled and manual_long_entry > 0 ? manual_long_entry : na, title="Manual Long Entry", text="Manual Buy", style=shape.labelup, location=location.belowbar, size=size.small, color=color.new(color.blue, 30), textcolor=color.white)
plotshape(manual_entry_enabled and manual_short_entry > 0 ? manual_short_entry : na, title="Manual Short Entry", text="Manual Sell", style=shape.labeldown, location=location.abovebar, size=size.small, color=color.new(color.orange, 30), textcolor=color.white)

// Original trend signals (only used when manual entry is disabled)
longSignal = ta.crossover(trend, 0)
shortSignal = ta.crossunder(trend, 0)

// Manual entry logic - trigger only once when values change
var prev_manual_long = 0.0
var prev_manual_short = 0.0

manual_long_trigger = manual_entry_enabled and manual_long_entry > 0 and manual_long_entry != prev_manual_long
manual_short_trigger = manual_entry_enabled and manual_short_entry > 0 and manual_short_entry != prev_manual_short

// Update previous values
if manual_long_trigger
    prev_manual_long := manual_long_entry
if manual_short_trigger
    prev_manual_short := manual_short_entry

// Combined signals - use manual entry when enabled, otherwise use trend signals
final_long_signal = manual_entry_enabled ? manual_long_trigger : longSignal
final_short_signal = manual_entry_enabled ? manual_short_trigger : shortSignal

var SL = 0.0
var TP1_lvl = 0.0
var TP2_lvl = 0.0
var TP3_lvl = 0.0
var Entry2_lvl = 0.0 // Declare variable for Entry 2 level
var entry_price = 0.0 // Store the actual entry price

var line entry_line = na
var line sl_line = na
var line tp1_line = na
var line tp2_line = na
var line tp3_line = na
var line entry2_line = na // Declare line for Entry 2

var label entry_label = na
var label sl_label = na
var label tp1_label = na
var label tp2_label = na
var label tp3_label = na
var label entry2_label = na // Declare label for Entry 2

if final_long_signal and shw_TP1
    // Set entry price based on mode
    entry_price := manual_entry_enabled ? manual_long_entry : close

    // Calculate SL and levels based on entry price
    SL := entry_price - volatility * sl_multiplier
    // Calculate Entry 2 level for long signal (below entry, above SL)
    Entry2_lvl := entry_price - math.abs(entry_price - SL) * entry2_multiplier
    TP1_lvl := entry_price + math.abs(entry_price - SL) * tp1_multiplier
    TP2_lvl := entry_price + math.abs(entry_price - SL) * tp2_multiplier
    TP3_lvl := entry_price + math.abs(entry_price - SL) * tp3_multiplier

    // Create lines and labels for current signal
    entry_line := line.new(bar_index, entry_price, bar_index, entry_price, color = green, width = 3)
    entry_label := label.new(bar_index, entry_price, text = (manual_entry_enabled ? "Manual " : "") + "Entry ▸ " + str.tostring(entry_price, format.mintick), style = label.style_label_left, color = green, textcolor = color.white)

    sl_line := line.new(bar_index, SL, bar_index, SL, color = color.new(red, 80), width = 3)
    sl_label := label.new(bar_index, SL, text = "✘ SL ▸ " + str.tostring(SL, format.mintick), style = label.style_label_left, color = color.new(red, 80), textcolor = color.white)

    // Entry 2 line and label
    entry2_line := line.new(bar_index, Entry2_lvl, bar_index, Entry2_lvl, color = color.new(color.gray, 80), width = 3) // Using gray for distinction
    entry2_label := label.new(bar_index, Entry2_lvl, text = "Entry 2 ▸ " + str.tostring(Entry2_lvl, format.mintick), style = label.style_label_left, color = color.new(color.gray, 80), textcolor = color.white)

    tp1_line := line.new(bar_index, TP1_lvl, bar_index, TP1_lvl, color = color.new(green, 80), width = 3)
    tp1_label := label.new(bar_index, TP1_lvl, text = " ✔ TP1 ▸ " + str.tostring(TP1_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp2_line := line.new(bar_index, TP2_lvl, bar_index, TP2_lvl, color = color.new(green, 80), width = 3)
    tp2_label := label.new(bar_index, TP2_lvl, text = " ✔ TP2 ▸ " + str.tostring(TP2_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp3_line := line.new(bar_index, TP3_lvl, bar_index, TP3_lvl, color = color.new(green, 80), width = 3)
    tp3_label := label.new(bar_index, TP3_lvl, text = " ✔ TP3 ▸ " + str.tostring(TP3_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    // Delete previous lines and labels
    line.delete(entry_line[1])
    label.delete(entry_label[1])

    line.delete(sl_line[1])
    label.delete(sl_label[1])

    line.delete(entry2_line[1]) // Delete previous Entry 2 line
    label.delete(entry2_label[1]) // Delete previous Entry 2 label

    line.delete(tp1_line[1])
    label.delete(tp1_label[1])

    line.delete(tp2_line[1])
    label.delete(tp2_label[1])

    line.delete(tp3_line[1])
    label.delete(tp3_label[1])
else
    // Extend lines and labels to current bar
    line.set_x2(entry_line, bar_index)
    label.set_x(entry_label, bar_index)

    line.set_x2(sl_line, bar_index)
    label.set_x(sl_label, bar_index)

    line.set_x2(entry2_line, bar_index) // Extend Entry 2 line
    label.set_x(entry2_label, bar_index) // Extend Entry 2 label

    line.set_x2(tp1_line, bar_index)
    label.set_x(tp1_label, bar_index)

    line.set_x2(tp2_line, bar_index)
    label.set_x(tp2_label, bar_index)

    line.set_x2(tp3_line, bar_index)
    label.set_x(tp3_label, bar_index)

if final_short_signal and shw_TP1
    // Set entry price based on mode
    entry_price := manual_entry_enabled ? manual_short_entry : close

    // Calculate SL and levels based on entry price
    SL := entry_price + volatility * sl_multiplier
    // Calculate Entry 2 level for short signal (above entry, below SL)
    Entry2_lvl := entry_price + math.abs(entry_price - SL) * entry2_multiplier
    TP1_lvl := entry_price - math.abs(entry_price - SL) * tp1_multiplier
    TP2_lvl := entry_price - math.abs(entry_price - SL) * tp2_multiplier
    TP3_lvl := entry_price - math.abs(entry_price - SL) * tp3_multiplier

    // Create lines and labels for current signal
    entry_line := line.new(bar_index, entry_price, bar_index, entry_price, color = red, width = 3)
    entry_label := label.new(bar_index, entry_price, text = (manual_entry_enabled ? "Manual " : "") + "Entry ▸ " + str.tostring(entry_price, format.mintick), style = label.style_label_left, color = red, textcolor = color.white)

    sl_line := line.new(bar_index, SL, bar_index, SL, color = color.new(red, 80), width = 3)
    sl_label := label.new(bar_index, SL, text = "✘ SL ▸ " + str.tostring(SL, format.mintick), style = label.style_label_left, color = color.new(red, 80), textcolor = color.white)

    // Entry 2 line and label
    entry2_line := line.new(bar_index, Entry2_lvl, bar_index, Entry2_lvl, color = color.new(color.gray, 80), width = 3) // Using gray for distinction
    entry2_label := label.new(bar_index, Entry2_lvl, text = "Entry 2 ▸ " + str.tostring(Entry2_lvl, format.mintick), style = label.style_label_left, color = color.new(color.gray, 80), textcolor = color.white)

    tp1_line := line.new(bar_index, TP1_lvl, bar_index, TP1_lvl, color = color.new(green, 80), width = 3)
    tp1_label := label.new(bar_index, TP1_lvl, text = " ✔ TP1 ▸ " + str.tostring(TP1_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp2_line := line.new(bar_index, TP2_lvl, bar_index, TP2_lvl, color = color.new(green, 80), width = 3)
    tp2_label := label.new(bar_index, TP2_lvl, text = " ✔ TP2 ▸ " + str.tostring(TP2_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp3_line := line.new(bar_index, TP3_lvl, bar_index, TP3_lvl, color = color.new(green, 80), width = 3)
    tp3_label := label.new(bar_index, TP3_lvl, text = " ✔ TP3 ▸ " + str.tostring(TP3_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    // Delete previous lines and labels
    line.delete(entry_line[1])
    label.delete(entry_label[1])

    line.delete(sl_line[1])
    label.delete(sl_label[1])

    line.delete(entry2_line[1]) // Delete previous Entry 2 line
    label.delete(entry2_label[1]) // Delete previous Entry 2 label

    line.delete(tp1_line[1])
    label.delete(tp1_label[1])

    line.delete(tp2_line[1])
    label.delete(tp2_label[1])

    line.delete(tp3_line[1])
    label.delete(tp3_label[1])
else
    // Extend lines and labels to current bar
    line.set_x2(entry_line, bar_index+40)
    label.set_x(entry_label, bar_index+40)

    line.set_x2(sl_line, bar_index+40)
    label.set_x(sl_label, bar_index+40)

    line.set_x2(entry2_line, bar_index+40) // Extend Entry 2 line
    label.set_x(entry2_label, bar_index+40) // Extend Entry 2 label

    line.set_x2(tp1_line, bar_index+40)
    label.set_x(tp1_label, bar_index+40)

    line.set_x2(tp2_line, bar_index+40)
    label.set_x(tp2_label, bar_index+40)

    line.set_x2(tp3_line, bar_index+40)
    label.set_x(tp3_label, bar_index+40)

liness = array.new_linefill()

// Add linefills. Note: The order here does not affect the label text order,
// but rather the visual layering of the filled areas.
liness.unshift(linefill.new(entry_line, sl_line, color.new(color.red, 95)))
liness.unshift(linefill.new(entry_line, tp3_line, color.new(color.green, 95)))


// Alert for trend changes
alertcondition(ta.crossover(close, SL), title="Trend Change: Bullish", message="Price crossed above SL - Potential Bullish Trend")
alertcondition(ta.crossunder(close, SL), title="Trend Change: Bearish", message="Price crossed below SL - Potential Bearish Trend")

// Alert for rejection signals
alertcondition(ta.crossover(high, SL) and close < SL, title="Rejection: Bearish", message="Price rejected at SL - Bearish Rejection")
alertcondition(ta.crossunder(low, SL) and close > SL, title="Rejection: Bullish", message="Price rejected at SL - Bullish Rejection")

// Alerts for TP hits
alertcondition(ta.crossover(close, TP1_lvl), title="TP1 Hit", message="Price reached TP1 level")
alertcondition(ta.crossover(close, TP2_lvl), title="TP2 Hit", message="Price reached TP2 level")
alertcondition(ta.crossover(close, TP3_lvl), title="TP3 Hit", message="Price reached TP3 level")

// Alert for Entry 2 hit
alertcondition(ta.crossover(close, Entry2_lvl) and trend == 1, title="Entry 2 Hit (Bullish)", message="Price reached Entry 2 level for long position")
alertcondition(ta.crossunder(close, Entry2_lvl) and trend == -1, title="Entry 2 Hit (Bearish)", message="Price reached Entry 2 level for short position")


//Multi MA with Custom Type
f_ma(source, length, mtype) =>
    mtype == 'SMA' ? ta.sma(source, length) : mtype == 'EMA' ? ta.ema(source, length) : ta.wma(source, length)

gr_ma = 'Optional MA\'s'
t_ma1 = 'MA #1'
t_ma2 = 'MA #2'
t_ma3 = 'MA #3'

// Inputs for MA1
show_ma1 = input.bool(false, t_ma1, inline = t_ma1, group = gr_ma)
ma1_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma1, group = gr_ma)
ma1_source = input.source(close, '', inline = t_ma1, group = gr_ma)
ma1_length = input.int(20, '', minval = 1, inline = t_ma1, group = gr_ma)
ma1_color = #59ff00
ma1 = f_ma(ma1_source, ma1_length, ma1_type)

// Inputs for MA2
show_ma2 = input.bool(false, t_ma2, inline = t_ma2, group = gr_ma)
ma2_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma2, group = gr_ma)
ma2_source = input.source(close, '', inline = t_ma2, group = gr_ma)
ma2_length = input.int(50, '', minval = 1, inline = t_ma2, group = gr_ma)
ma2_color = #ff7b5a
ma2 = f_ma(ma2_source, ma2_length, ma2_type)

// Inputs for MA3
show_ma3 = input.bool(false, t_ma3, inline = t_ma3, group = gr_ma)
ma3_type = input.string('SMA', '', options = ['SMA', 'EMA', 'WMA', 'HMA'], inline = t_ma3, group = gr_ma)
ma3_source = input.source(close, '', inline = t_ma3, group = gr_ma)
ma3_length = input.int(100, '', minval = 1, inline = t_ma3, group = gr_ma)
ma3_color = #ffc100
ma3 = f_ma(ma3_source, ma3_length, ma3_type)

// Plot and fill logic for MA1
plot0_ma1 = plot(ma1_source, display = display.none, editable = false)
css_ma1 = ma1_source > ma1 ? #0cb51a : #ff1100
plot1_ma1 = plot(show_ma1 ? ma1 : na, title = t_ma1, color = css_ma1, linewidth = 1, display = show_ma1 ? display.all : display.none)
fill_css_ma1 = ma1_source > ma1 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma1, plot1_ma1, color = fill_css_ma1, title = 'Fill MA1')

// Plot and fill logic for MA2
plot0_ma2 = plot(ma2_source, display = display.none, editable = false)
css_ma2 = ma2_source > ma2 ? #0cb51a : #ff1100
plot1_ma2 = plot(show_ma2 ? ma2 : na, title = t_ma2, color = css_ma2, linewidth = 1, display = show_ma2 ? display.all : display.none)
fill_css_ma2 = ma2_source > ma2 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma2, plot1_ma2, color = fill_css_ma2, title = 'Fill MA2')

// Plot and fill logic for MA3
plot0_ma3 = plot(ma3_source, display = display.none, editable = false)
css_ma3 = ma3_source > ma3 ? #0cb51a : #ff1100
plot1_ma3 = plot(show_ma3 ? ma3 : na, title = t_ma3, color = css_ma3, linewidth = 1, display = show_ma3 ? display.all : display.none)
fill_css_ma3 = ma3_source > ma3 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma3, plot1_ma3, color = fill_css_ma3, title = 'Fill MA3')