// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © GoldenEntry

//@version=6
indicator("Manual Entry TP SL [Golden Entry]", "Manual Entry TP SL", overlay = true)

// Manual Entry Settings
manual_long_entry = input.float(0, title="Long Entry Price", minval=0, step=0.01, group="Manual Entry",
     tooltip="Manual entry price for long position. Set to 0 to disable long entry.")
manual_short_entry = input.float(0, title="Short Entry Price", minval=0, step=0.01, group="Manual Entry",
     tooltip="Manual entry price for short position. Set to 0 to disable short entry.")

// Target Settings
atr_period = input.int(14, title="ATR Period", minval=1, group="Target Settings",
     tooltip="Number of bars used to calculate the Average True Range for position sizing and targets.")
sl_multiplier = input.float(5, title="Stop Loss ATR Multiplier", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Multiplier applied to ATR to determine stop loss distance from entry.")
entry2_multiplier = input.float(0.5, title="Entry 2 Multiplier", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Multiple of SL distance for a secondary entry target.")
tp1_multiplier = input.float(0.5, title="TP1 Multiplier", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Multiple of SL distance for first take profit target.")
tp2_multiplier = input.float(1.0, title="TP2 Multiplier", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Multiple of SL distance for second take profit target.")
tp3_multiplier = input.float(1.5, title="TP3 Multiplier", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Multiple of SL distance for third take profit target.")

// Appearance settings
green = input.color(#00ffbb, title="Bullish Color", group="Appearance")
red = input.color(#ff1100, title="Bearish Color", group="Appearance")

volatility = ta.atr(atr_period)

// Manual entry logic - trigger when values change
var prev_manual_long = 0.0
var prev_manual_short = 0.0

manual_long_trigger = manual_long_entry > 0 and manual_long_entry != prev_manual_long
manual_short_trigger = manual_short_entry > 0 and manual_short_entry != prev_manual_short

// Update previous values
if manual_long_trigger
    prev_manual_long := manual_long_entry
if manual_short_trigger
    prev_manual_short := manual_short_entry

// Show manual entry points only when triggered (not continuously)
plotshape(manual_long_trigger ? manual_long_entry : na, title="Manual Long Entry", text="Long", style=shape.labelup, location=location.belowbar, size=size.small, color=color.new(color.blue, 30), textcolor=color.white)
plotshape(manual_short_trigger ? manual_short_entry : na, title="Manual Short Entry", text="Short", style=shape.labeldown, location=location.abovebar, size=size.small, color=color.new(color.orange, 30), textcolor=color.white)

var SL = 0.0
var TP1_lvl = 0.0
var TP2_lvl = 0.0
var TP3_lvl = 0.0
var Entry2_lvl = 0.0
var entry_price = 0.0

var line entry_line = na
var line sl_line = na
var line tp1_line = na
var line tp2_line = na
var line tp3_line = na
var line entry2_line = na

var label entry_label = na
var label sl_label = na
var label tp1_label = na
var label tp2_label = na
var label tp3_label = na
var label entry2_label = na

// Long position setup
if manual_long_trigger
    entry_price := manual_long_entry

    // Calculate SL and levels based on entry price
    SL := entry_price - volatility * sl_multiplier
    // Calculate Entry 2 level for long signal (below entry, above SL)
    Entry2_lvl := entry_price - math.abs(entry_price - SL) * entry2_multiplier
    TP1_lvl := entry_price + math.abs(entry_price - SL) * tp1_multiplier
    TP2_lvl := entry_price + math.abs(entry_price - SL) * tp2_multiplier
    TP3_lvl := entry_price + math.abs(entry_price - SL) * tp3_multiplier

    // Delete previous lines and labels first
    if not na(entry_line)
        line.delete(entry_line)
        label.delete(entry_label)
        line.delete(sl_line)
        label.delete(sl_label)
        line.delete(entry2_line)
        label.delete(entry2_label)
        line.delete(tp1_line)
        label.delete(tp1_label)
        line.delete(tp2_line)
        label.delete(tp2_label)
        line.delete(tp3_line)
        label.delete(tp3_label)

    // Create new lines and labels
    entry_line := line.new(bar_index, entry_price, bar_index + 50, entry_price, color = green, width = 3)
    entry_label := label.new(bar_index + 50, entry_price, text = "Long Entry ▸ " + str.tostring(entry_price, format.mintick), style = label.style_label_left, color = green, textcolor = color.white)

    sl_line := line.new(bar_index, SL, bar_index + 50, SL, color = color.new(red, 80), width = 3)
    sl_label := label.new(bar_index + 50, SL, text = "✘ SL ▸ " + str.tostring(SL, format.mintick), style = label.style_label_left, color = color.new(red, 80), textcolor = color.white)

    entry2_line := line.new(bar_index, Entry2_lvl, bar_index + 50, Entry2_lvl, color = color.new(color.gray, 80), width = 3)
    entry2_label := label.new(bar_index + 50, Entry2_lvl, text = "Entry 2 ▸ " + str.tostring(Entry2_lvl, format.mintick), style = label.style_label_left, color = color.new(color.gray, 80), textcolor = color.white)

    tp1_line := line.new(bar_index, TP1_lvl, bar_index + 50, TP1_lvl, color = color.new(green, 80), width = 3)
    tp1_label := label.new(bar_index + 50, TP1_lvl, text = " ✔ TP1 ▸ " + str.tostring(TP1_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp2_line := line.new(bar_index, TP2_lvl, bar_index + 50, TP2_lvl, color = color.new(green, 80), width = 3)
    tp2_label := label.new(bar_index + 50, TP2_lvl, text = " ✔ TP2 ▸ " + str.tostring(TP2_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp3_line := line.new(bar_index, TP3_lvl, bar_index + 50, TP3_lvl, color = color.new(green, 80), width = 3)
    tp3_label := label.new(bar_index + 50, TP3_lvl, text = " ✔ TP3 ▸ " + str.tostring(TP3_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)


// Short position setup
if manual_short_trigger
    entry_price := manual_short_entry

    // Calculate SL and levels based on entry price
    SL := entry_price + volatility * sl_multiplier
    // Calculate Entry 2 level for short signal (above entry, below SL)
    Entry2_lvl := entry_price + math.abs(entry_price - SL) * entry2_multiplier
    TP1_lvl := entry_price - math.abs(entry_price - SL) * tp1_multiplier
    TP2_lvl := entry_price - math.abs(entry_price - SL) * tp2_multiplier
    TP3_lvl := entry_price - math.abs(entry_price - SL) * tp3_multiplier

    // Delete previous lines and labels first
    if not na(entry_line)
        line.delete(entry_line)
        label.delete(entry_label)
        line.delete(sl_line)
        label.delete(sl_label)
        line.delete(entry2_line)
        label.delete(entry2_label)
        line.delete(tp1_line)
        label.delete(tp1_label)
        line.delete(tp2_line)
        label.delete(tp2_label)
        line.delete(tp3_line)
        label.delete(tp3_label)

    // Create new lines and labels
    entry_line := line.new(bar_index, entry_price, bar_index + 50, entry_price, color = red, width = 3)
    entry_label := label.new(bar_index + 50, entry_price, text = "Short Entry ▸ " + str.tostring(entry_price, format.mintick), style = label.style_label_left, color = red, textcolor = color.white)

    sl_line := line.new(bar_index, SL, bar_index + 50, SL, color = color.new(red, 80), width = 3)
    sl_label := label.new(bar_index + 50, SL, text = "✘ SL ▸ " + str.tostring(SL, format.mintick), style = label.style_label_left, color = color.new(red, 80), textcolor = color.white)

    entry2_line := line.new(bar_index, Entry2_lvl, bar_index + 50, Entry2_lvl, color = color.new(color.gray, 80), width = 3)
    entry2_label := label.new(bar_index + 50, Entry2_lvl, text = "Entry 2 ▸ " + str.tostring(Entry2_lvl, format.mintick), style = label.style_label_left, color = color.new(color.gray, 80), textcolor = color.white)

    tp1_line := line.new(bar_index, TP1_lvl, bar_index + 50, TP1_lvl, color = color.new(green, 80), width = 3)
    tp1_label := label.new(bar_index + 50, TP1_lvl, text = " ✔ TP1 ▸ " + str.tostring(TP1_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp2_line := line.new(bar_index, TP2_lvl, bar_index + 50, TP2_lvl, color = color.new(green, 80), width = 3)
    tp2_label := label.new(bar_index + 50, TP2_lvl, text = " ✔ TP2 ▸ " + str.tostring(TP2_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp3_line := line.new(bar_index, TP3_lvl, bar_index + 50, TP3_lvl, color = color.new(green, 80), width = 3)
    tp3_label := label.new(bar_index + 50, TP3_lvl, text = " ✔ TP3 ▸ " + str.tostring(TP3_lvl, format.mintick), style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)


liness = array.new_linefill()

// Add linefills. Note: The order here does not affect the label text order,
// but rather the visual layering of the filled areas.
liness.unshift(linefill.new(entry_line, sl_line, color.new(color.red, 95)))
liness.unshift(linefill.new(entry_line, tp3_line, color.new(color.green, 95)))


// Alert for trend changes
alertcondition(ta.crossover(close, SL), title="Trend Change: Bullish", message="Price crossed above SL - Potential Bullish Trend")
alertcondition(ta.crossunder(close, SL), title="Trend Change: Bearish", message="Price crossed below SL - Potential Bearish Trend")

// Alert for rejection signals
alertcondition(ta.crossover(high, SL) and close < SL, title="Rejection: Bearish", message="Price rejected at SL - Bearish Rejection")
alertcondition(ta.crossunder(low, SL) and close > SL, title="Rejection: Bullish", message="Price rejected at SL - Bullish Rejection")

// Alerts for TP hits
alertcondition(ta.crossover(close, TP1_lvl), title="TP1 Hit", message="Price reached TP1 level")
alertcondition(ta.crossover(close, TP2_lvl), title="TP2 Hit", message="Price reached TP2 level")
alertcondition(ta.crossover(close, TP3_lvl), title="TP3 Hit", message="Price reached TP3 level")

// Alert for Entry 2 hit
alertcondition(ta.crossunder(close, Entry2_lvl) and manual_long_entry > 0, title="Entry 2 Hit (Long)", message="Price reached Entry 2 level for long position")
alertcondition(ta.crossover(close, Entry2_lvl) and manual_short_entry > 0, title="Entry 2 Hit (Short)", message="Price reached Entry 2 level for short position")


