//@version=6
indicator("Manual Entry TP SL", overlay=true, max_lines_count=500, max_labels_count=500)

// Manual Entry Settings
long_entry = input.float(0, "Long Entry Price", group="Manual Entry")
short_entry = input.float(0, "Short Entry Price", group="Manual Entry")

// Target Settings
sl_pct = input.float(2.0, "Stop Loss %", group="Target Settings")
tp1_pct = input.float(1.0, "TP1 %", group="Target Settings")
tp2_pct = input.float(2.0, "TP2 %", group="Target Settings")
tp3_pct = input.float(3.0, "TP3 %", group="Target Settings")

// Debug
show_debug = input.bool(true, "Show Debug Info", group="Debug")

// Variables to track previous values
var float prev_long = 0.0
var float prev_short = 0.0

// Check for triggers
long_triggered = long_entry > 0 and long_entry != prev_long
short_triggered = short_entry > 0 and short_entry != prev_short

// Update previous values
if long_triggered
    prev_long := long_entry
if short_triggered
    prev_short := short_entry

// Debug info
if show_debug
    debug_text = "Long: " + str.tostring(long_entry) + " | Trigger: " + str.tostring(long_triggered) + " | Bar: " + str.tostring(bar_index)
    label.new(bar_index, high * 1.01, debug_text, style=label.style_label_down, color=color.yellow, textcolor=color.black, size=size.small)

// Show entry markers
plotshape(long_triggered, "Long Entry", shape.triangleup, location.belowbar, color.blue, size=size.normal, text="LONG")
plotshape(short_triggered, "Short Entry", shape.triangledown, location.abovebar, color.orange, size=size.normal, text="SHORT")

// LONG SETUP
if long_triggered
    // Calculate levels
    entry = long_entry
    sl = entry * (1 - sl_pct / 100)
    tp1 = entry * (1 + tp1_pct / 100)
    tp2 = entry * (1 + tp2_pct / 100)
    tp3 = entry * (1 + tp3_pct / 100)

    // Create lines (simple approach)
    line.new(bar_index, entry, bar_index + 30, entry, color=color.green, width=3)
    line.new(bar_index, sl, bar_index + 30, sl, color=color.red, width=2)
    line.new(bar_index, tp1, bar_index + 30, tp1, color=color.lime, width=2)
    line.new(bar_index, tp2, bar_index + 30, tp2, color=color.lime, width=2)
    line.new(bar_index, tp3, bar_index + 30, tp3, color=color.lime, width=2)

    // Create labels
    label.new(bar_index + 30, entry, "Entry: " + str.tostring(entry), color=color.green, textcolor=color.white)
    label.new(bar_index + 30, sl, "SL: " + str.tostring(sl), color=color.red, textcolor=color.white)
    label.new(bar_index + 30, tp1, "TP1: " + str.tostring(tp1), color=color.lime, textcolor=color.black)
    label.new(bar_index + 30, tp2, "TP2: " + str.tostring(tp2), color=color.lime, textcolor=color.black)
    label.new(bar_index + 30, tp3, "TP3: " + str.tostring(tp3), color=color.lime, textcolor=color.black)

// SHORT SETUP
if short_triggered
    // Calculate levels
    entry = short_entry
    sl = entry * (1 + sl_pct / 100)
    tp1 = entry * (1 - tp1_pct / 100)
    tp2 = entry * (1 - tp2_pct / 100)
    tp3 = entry * (1 - tp3_pct / 100)

    // Create lines (simple approach)
    line.new(bar_index, entry, bar_index + 30, entry, color=color.red, width=3)
    line.new(bar_index, sl, bar_index + 30, sl, color=color.red, width=2)
    line.new(bar_index, tp1, bar_index + 30, tp1, color=color.lime, width=2)
    line.new(bar_index, tp2, bar_index + 30, tp2, color=color.lime, width=2)
    line.new(bar_index, tp3, bar_index + 30, tp3, color=color.lime, width=2)

    // Create labels
    label.new(bar_index + 30, entry, "Entry: " + str.tostring(entry), color=color.red, textcolor=color.white)
    label.new(bar_index + 30, sl, "SL: " + str.tostring(sl), color=color.red, textcolor=color.white)
    label.new(bar_index + 30, tp1, "TP1: " + str.tostring(tp1), color=color.lime, textcolor=color.black)
    label.new(bar_index + 30, tp2, "TP2: " + str.tostring(tp2), color=color.lime, textcolor=color.black)
    label.new(bar_index + 30, tp3, "TP3: " + str.tostring(tp3), color=color.lime, textcolor=color.black)





