// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © GoldenEntry

//@version=6
indicator("Manual Entry TP SL [Golden Entry]", "Manual Entry TP SL", overlay = true)

// Manual Entry Settings
manual_long_entry = input.float(0, title="Long Entry Price", minval=0, step=0.01, group="Manual Entry")
manual_short_entry = input.float(0, title="Short Entry Price", minval=0, step=0.01, group="Manual Entry")

// Target Settings (Percentage Based)
sl_percentage = input.float(2.0, title="Stop Loss %", minval=0.1, step=0.1, group="Target Settings")
tp1_percentage = input.float(1.0, title="TP1 %", minval=0.1, step=0.1, group="Target Settings")
tp2_percentage = input.float(2.0, title="TP2 %", minval=0.1, step=0.1, group="Target Settings")
tp3_percentage = input.float(3.0, title="TP3 %", minval=0.1, step=0.1, group="Target Settings")

// Manual entry logic - trigger when values change
var prev_manual_long = 0.0
var prev_manual_short = 0.0

manual_long_trigger = manual_long_entry > 0 and manual_long_entry != prev_manual_long
manual_short_trigger = manual_short_entry > 0 and manual_short_entry != prev_manual_short

// Update previous values
if manual_long_trigger
    prev_manual_long := manual_long_entry
if manual_short_trigger
    prev_manual_short := manual_short_entry

// Show entry markers
plotshape(manual_long_trigger ? manual_long_entry : na, title="Long Entry", text="LONG", style=shape.triangleup, location=location.belowbar, size=size.normal, color=color.blue, textcolor=color.white)
plotshape(manual_short_trigger ? manual_short_entry : na, title="Short Entry", text="SHORT", style=shape.triangledown, location=location.abovebar, size=size.normal, color=color.orange, textcolor=color.white)

// Long position setup - create lines immediately when triggered
if manual_long_trigger
    entry_price = manual_long_entry

    // Calculate levels
    sl_level = entry_price * (1 - sl_percentage / 100)
    tp1_level = entry_price * (1 + tp1_percentage / 100)
    tp2_level = entry_price * (1 + tp2_percentage / 100)
    tp3_level = entry_price * (1 + tp3_percentage / 100)

    // Create lines from current bar to 50 bars ahead
    line.new(bar_index, entry_price, bar_index + 50, entry_price, color=color.green, width=3)
    line.new(bar_index, sl_level, bar_index + 50, sl_level, color=color.red, width=2)
    line.new(bar_index, tp1_level, bar_index + 50, tp1_level, color=color.lime, width=2)
    line.new(bar_index, tp2_level, bar_index + 50, tp2_level, color=color.lime, width=2)
    line.new(bar_index, tp3_level, bar_index + 50, tp3_level, color=color.lime, width=2)

    // Create labels at the end of lines
    label.new(bar_index + 50, entry_price, "Entry: " + str.tostring(entry_price), style=label.style_label_left, color=color.green, textcolor=color.white)
    label.new(bar_index + 50, sl_level, "SL: " + str.tostring(sl_level) + " (-" + str.tostring(sl_percentage) + "%)", style=label.style_label_left, color=color.red, textcolor=color.white)
    label.new(bar_index + 50, tp1_level, "TP1: " + str.tostring(tp1_level) + " (+" + str.tostring(tp1_percentage) + "%)", style=label.style_label_left, color=color.lime, textcolor=color.black)
    label.new(bar_index + 50, tp2_level, "TP2: " + str.tostring(tp2_level) + " (+" + str.tostring(tp2_percentage) + "%)", style=label.style_label_left, color=color.lime, textcolor=color.black)
    label.new(bar_index + 50, tp3_level, "TP3: " + str.tostring(tp3_level) + " (+" + str.tostring(tp3_percentage) + "%)", style=label.style_label_left, color=color.lime, textcolor=color.black)


// Short position setup - create lines immediately when triggered
if manual_short_trigger
    entry_price = manual_short_entry

    // Calculate levels
    sl_level = entry_price * (1 + sl_percentage / 100)
    tp1_level = entry_price * (1 - tp1_percentage / 100)
    tp2_level = entry_price * (1 - tp2_percentage / 100)
    tp3_level = entry_price * (1 - tp3_percentage / 100)

    // Create lines from current bar to 50 bars ahead
    line.new(bar_index, entry_price, bar_index + 50, entry_price, color=color.red, width=3)
    line.new(bar_index, sl_level, bar_index + 50, sl_level, color=color.red, width=2)
    line.new(bar_index, tp1_level, bar_index + 50, tp1_level, color=color.lime, width=2)
    line.new(bar_index, tp2_level, bar_index + 50, tp2_level, color=color.lime, width=2)
    line.new(bar_index, tp3_level, bar_index + 50, tp3_level, color=color.lime, width=2)

    // Create labels at the end of lines
    label.new(bar_index + 50, entry_price, "Entry: " + str.tostring(entry_price), style=label.style_label_left, color=color.red, textcolor=color.white)
    label.new(bar_index + 50, sl_level, "SL: " + str.tostring(sl_level) + " (+" + str.tostring(sl_percentage) + "%)", style=label.style_label_left, color=color.red, textcolor=color.white)
    label.new(bar_index + 50, tp1_level, "TP1: " + str.tostring(tp1_level) + " (-" + str.tostring(tp1_percentage) + "%)", style=label.style_label_left, color=color.lime, textcolor=color.black)
    label.new(bar_index + 50, tp2_level, "TP2: " + str.tostring(tp2_level) + " (-" + str.tostring(tp2_percentage) + "%)", style=label.style_label_left, color=color.lime, textcolor=color.black)
    label.new(bar_index + 50, tp3_level, "TP3: " + str.tostring(tp3_level) + " (-" + str.tostring(tp3_percentage) + "%)", style=label.style_label_left, color=color.lime, textcolor=color.black)


liness = array.new_linefill()

// Add linefills. Note: The order here does not affect the label text order,
// but rather the visual layering of the filled areas.
liness.unshift(linefill.new(entry_line, sl_line, color.new(color.red, 95)))
liness.unshift(linefill.new(entry_line, tp3_line, color.new(color.green, 95)))


// Alert for trend changes
alertcondition(ta.crossover(close, SL), title="Trend Change: Bullish", message="Price crossed above SL - Potential Bullish Trend")
alertcondition(ta.crossunder(close, SL), title="Trend Change: Bearish", message="Price crossed below SL - Potential Bearish Trend")

// Alert for rejection signals
alertcondition(ta.crossover(high, SL) and close < SL, title="Rejection: Bearish", message="Price rejected at SL - Bearish Rejection")
alertcondition(ta.crossunder(low, SL) and close > SL, title="Rejection: Bullish", message="Price rejected at SL - Bullish Rejection")

// Alerts for TP hits
alertcondition(ta.crossover(close, TP1_lvl), title="TP1 Hit", message="Price reached TP1 level")
alertcondition(ta.crossover(close, TP2_lvl), title="TP2 Hit", message="Price reached TP2 level")
alertcondition(ta.crossover(close, TP3_lvl), title="TP3 Hit", message="Price reached TP3 level")

// Alert for Entry 2 hit
alertcondition(ta.crossunder(close, Entry2_lvl) and manual_long_entry > 0, title="Entry 2 Hit (Long)", message="Price reached Entry 2 level for long position")
alertcondition(ta.crossover(close, Entry2_lvl) and manual_short_entry > 0, title="Entry 2 Hit (Short)", message="Price reached Entry 2 level for short position")


