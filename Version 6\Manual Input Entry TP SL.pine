// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © GoldenEntry

//@version=6
indicator("Manual Entry TP SL [Golden Entry]", "Manual Entry TP SL", overlay = true)

// Manual Entry Settings
manual_long_entry = input.float(0, title="Long Entry Price", minval=0, step=0.01, group="Manual Entry",
     tooltip="Manual entry price for long position. Set to 0 to disable long entry.")
manual_short_entry = input.float(0, title="Short Entry Price", minval=0, step=0.01, group="Manual Entry",
     tooltip="Manual entry price for short position. Set to 0 to disable short entry.")

// Line Length Settings
line_length = input.int(50, title="Line Length (bars)", minval=1, maxval=500, group="Line Settings",
     tooltip="Number of bars the lines will extend to the right from entry point.")
start_from_current = input.bool(true, title="Start from Current Bar", group="Line Settings",
     tooltip="If enabled, lines start from current bar. If disabled, lines start from entry trigger bar.")

// Target Settings (Percentage Based)
sl_percentage = input.float(2.0, title="Stop Loss %", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Stop loss distance as percentage from entry price.")
entry2_percentage = input.float(1.0, title="Entry 2 %", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Entry 2 distance as percentage from entry price.")
tp1_percentage = input.float(1.0, title="TP1 %", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Take profit 1 distance as percentage from entry price.")
tp2_percentage = input.float(2.0, title="TP2 %", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Take profit 2 distance as percentage from entry price.")
tp3_percentage = input.float(3.0, title="TP3 %", minval=0.1, step=0.1, group="Target Settings",
     tooltip="Take profit 3 distance as percentage from entry price.")

// Appearance settings
green = input.color(#00ffbb, title="Bullish Color", group="Appearance")
red = input.color(#ff1100, title="Bearish Color", group="Appearance")

// No ATR calculation needed - using percentage based levels

// Line positioning logic
var int entry_trigger_bar = na
line_start_bar = start_from_current ? bar_index : (na(entry_trigger_bar) ? bar_index : entry_trigger_bar)
line_end_bar = line_start_bar + line_length

// Manual entry logic - trigger when values change
var prev_manual_long = 0.0
var prev_manual_short = 0.0

manual_long_trigger = manual_long_entry > 0 and manual_long_entry != prev_manual_long
manual_short_trigger = manual_short_entry > 0 and manual_short_entry != prev_manual_short

// Update previous values and store trigger bar
if manual_long_trigger
    prev_manual_long := manual_long_entry
    entry_trigger_bar := bar_index
if manual_short_trigger
    prev_manual_short := manual_short_entry
    entry_trigger_bar := bar_index

// Show entry markers only when triggered (not continuously)
plotshape(manual_long_trigger ? manual_long_entry : na, title="Long Entry Trigger", text="LONG", style=shape.triangleup, location=location.belowbar, size=size.normal, color=color.new(color.blue, 0), textcolor=color.white)
plotshape(manual_short_trigger ? manual_short_entry : na, title="Short Entry Trigger", text="SHORT", style=shape.triangledown, location=location.abovebar, size=size.normal, color=color.new(color.orange, 0), textcolor=color.white)

var SL = 0.0
var TP1_lvl = 0.0
var TP2_lvl = 0.0
var TP3_lvl = 0.0
var Entry2_lvl = 0.0
var entry_price = 0.0

var line entry_line = na
var line sl_line = na
var line tp1_line = na
var line tp2_line = na
var line tp3_line = na
var line entry2_line = na

var label entry_label = na
var label sl_label = na
var label tp1_label = na
var label tp2_label = na
var label tp3_label = na
var label entry2_label = na

// Long position setup
if manual_long_trigger
    entry_price := manual_long_entry

    // Calculate SL and levels based on percentage from entry price
    SL := entry_price * (1 - sl_percentage / 100)
    Entry2_lvl := entry_price * (1 - entry2_percentage / 100)
    TP1_lvl := entry_price * (1 + tp1_percentage / 100)
    TP2_lvl := entry_price * (1 + tp2_percentage / 100)
    TP3_lvl := entry_price * (1 + tp3_percentage / 100)

    // Delete previous lines and labels
    if not na(entry_line)
        line.delete(entry_line)
        label.delete(entry_label)
        line.delete(sl_line)
        label.delete(sl_label)
        line.delete(entry2_line)
        label.delete(entry2_label)
        line.delete(tp1_line)
        label.delete(tp1_label)
        line.delete(tp2_line)
        label.delete(tp2_label)
        line.delete(tp3_line)
        label.delete(tp3_label)

    // Create new lines and labels with controlled positioning
    entry_line := line.new(line_start_bar, entry_price, line_end_bar, entry_price, color = green, width = 3)
    entry_label := label.new(line_end_bar, entry_price, text = "Long Entry ▸ " + str.tostring(entry_price, format.mintick), style = label.style_label_left, color = green, textcolor = color.white)

    sl_line := line.new(line_start_bar, SL, line_end_bar, SL, color = color.new(red, 80), width = 3)
    sl_label := label.new(line_end_bar, SL, text = "✘ SL ▸ " + str.tostring(SL, format.mintick) + " (-" + str.tostring(sl_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(red, 80), textcolor = color.white)

    entry2_line := line.new(line_start_bar, Entry2_lvl, line_end_bar, Entry2_lvl, color = color.new(color.gray, 80), width = 3)
    entry2_label := label.new(line_end_bar, Entry2_lvl, text = "Entry 2 ▸ " + str.tostring(Entry2_lvl, format.mintick) + " (-" + str.tostring(entry2_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(color.gray, 80), textcolor = color.white)

    tp1_line := line.new(line_start_bar, TP1_lvl, line_end_bar, TP1_lvl, color = color.new(green, 80), width = 3)
    tp1_label := label.new(line_end_bar, TP1_lvl, text = " ✔ TP1 ▸ " + str.tostring(TP1_lvl, format.mintick) + " (+" + str.tostring(tp1_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp2_line := line.new(line_start_bar, TP2_lvl, line_end_bar, TP2_lvl, color = color.new(green, 80), width = 3)
    tp2_label := label.new(line_end_bar, TP2_lvl, text = " ✔ TP2 ▸ " + str.tostring(TP2_lvl, format.mintick) + " (+" + str.tostring(tp2_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp3_line := line.new(line_start_bar, TP3_lvl, line_end_bar, TP3_lvl, color = color.new(green, 80), width = 3)
    tp3_label := label.new(line_end_bar, TP3_lvl, text = " ✔ TP3 ▸ " + str.tostring(TP3_lvl, format.mintick) + " (+" + str.tostring(tp3_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

// No need to extend lines - they have fixed length


// Short position setup
if manual_short_trigger
    entry_price := manual_short_entry

    // Calculate SL and levels based on percentage from entry price
    SL := entry_price * (1 + sl_percentage / 100)
    Entry2_lvl := entry_price * (1 + entry2_percentage / 100)
    TP1_lvl := entry_price * (1 - tp1_percentage / 100)
    TP2_lvl := entry_price * (1 - tp2_percentage / 100)
    TP3_lvl := entry_price * (1 - tp3_percentage / 100)

    // Delete previous lines and labels
    if not na(entry_line)
        line.delete(entry_line)
        label.delete(entry_label)
        line.delete(sl_line)
        label.delete(sl_label)
        line.delete(entry2_line)
        label.delete(entry2_label)
        line.delete(tp1_line)
        label.delete(tp1_label)
        line.delete(tp2_line)
        label.delete(tp2_label)
        line.delete(tp3_line)
        label.delete(tp3_label)

    // Create new lines and labels with controlled positioning
    entry_line := line.new(line_start_bar, entry_price, line_end_bar, entry_price, color = red, width = 3)
    entry_label := label.new(line_end_bar, entry_price, text = "Short Entry ▸ " + str.tostring(entry_price, format.mintick), style = label.style_label_left, color = red, textcolor = color.white)

    sl_line := line.new(line_start_bar, SL, line_end_bar, SL, color = color.new(red, 80), width = 3)
    sl_label := label.new(line_end_bar, SL, text = "✘ SL ▸ " + str.tostring(SL, format.mintick) + " (+" + str.tostring(sl_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(red, 80), textcolor = color.white)

    entry2_line := line.new(line_start_bar, Entry2_lvl, line_end_bar, Entry2_lvl, color = color.new(color.gray, 80), width = 3)
    entry2_label := label.new(line_end_bar, Entry2_lvl, text = "Entry 2 ▸ " + str.tostring(Entry2_lvl, format.mintick) + " (+" + str.tostring(entry2_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(color.gray, 80), textcolor = color.white)

    tp1_line := line.new(line_start_bar, TP1_lvl, line_end_bar, TP1_lvl, color = color.new(green, 80), width = 3)
    tp1_label := label.new(line_end_bar, TP1_lvl, text = " ✔ TP1 ▸ " + str.tostring(TP1_lvl, format.mintick) + " (-" + str.tostring(tp1_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp2_line := line.new(line_start_bar, TP2_lvl, line_end_bar, TP2_lvl, color = color.new(green, 80), width = 3)
    tp2_label := label.new(line_end_bar, TP2_lvl, text = " ✔ TP2 ▸ " + str.tostring(TP2_lvl, format.mintick) + " (-" + str.tostring(tp2_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

    tp3_line := line.new(line_start_bar, TP3_lvl, line_end_bar, TP3_lvl, color = color.new(green, 80), width = 3)
    tp3_label := label.new(line_end_bar, TP3_lvl, text = " ✔ TP3 ▸ " + str.tostring(TP3_lvl, format.mintick) + " (-" + str.tostring(tp3_percentage, "#.#") + "%)", style = label.style_label_left, color = color.new(green, 80), textcolor = color.white)

// No need to extend lines - they have fixed length


liness = array.new_linefill()

// Add linefills. Note: The order here does not affect the label text order,
// but rather the visual layering of the filled areas.
liness.unshift(linefill.new(entry_line, sl_line, color.new(color.red, 95)))
liness.unshift(linefill.new(entry_line, tp3_line, color.new(color.green, 95)))


// Alert for trend changes
alertcondition(ta.crossover(close, SL), title="Trend Change: Bullish", message="Price crossed above SL - Potential Bullish Trend")
alertcondition(ta.crossunder(close, SL), title="Trend Change: Bearish", message="Price crossed below SL - Potential Bearish Trend")

// Alert for rejection signals
alertcondition(ta.crossover(high, SL) and close < SL, title="Rejection: Bearish", message="Price rejected at SL - Bearish Rejection")
alertcondition(ta.crossunder(low, SL) and close > SL, title="Rejection: Bullish", message="Price rejected at SL - Bullish Rejection")

// Alerts for TP hits
alertcondition(ta.crossover(close, TP1_lvl), title="TP1 Hit", message="Price reached TP1 level")
alertcondition(ta.crossover(close, TP2_lvl), title="TP2 Hit", message="Price reached TP2 level")
alertcondition(ta.crossover(close, TP3_lvl), title="TP3 Hit", message="Price reached TP3 level")

// Alert for Entry 2 hit
alertcondition(ta.crossunder(close, Entry2_lvl) and manual_long_entry > 0, title="Entry 2 Hit (Long)", message="Price reached Entry 2 level for long position")
alertcondition(ta.crossover(close, Entry2_lvl) and manual_short_entry > 0, title="Entry 2 Hit (Short)", message="Price reached Entry 2 level for short position")


