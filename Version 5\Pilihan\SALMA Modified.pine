// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © RedKTrader

//@version=5

indicator('RedK SmoothAndLazyMA', shorttitle = 'SALMA v3.0', overlay = true, timeframe = '', timeframe_gaps = false)

g_slm = 'SALMA'
// Corrects price points within specific StdDev band before calculting a smoothed WMA

show_salma  = input.bool(true, title="Show SALMA Line", group = g_slm) // Menambahkan input untuk show/hide SALMA
price       = input(close, 'Source', group = g_slm)
length      = input.int(10, 'Length', minval=1, group = g_slm)
smooth      = input.int(3, 'Extra Smooth [1 = None]', minval=1, group = g_slm)

mult        = input.float(0.3, minval=0.05, maxval=3, step=0.05,
  title='Width',  inline = 'SD Channel', group='Volatility Filter (SD Channel)')

sd_len      = input.int(5, minval=1,
  title='Length', inline = 'SD Channel', group='Volatility Filter (SD Channel)')

baseline    = ta.wma(price, sd_len)
dev         = mult * ta.stdev(price, sd_len)
upper       = baseline + dev
lower       = baseline - dev

cprice      = price > upper ? upper : price < lower ? lower : price

// Uncomment these code lines to expose the base StdDev channel used as volatility filter
//plot (baseline, "Base MA")
//plot(upper, "Upper Band", color=color.green)
//plot(lower, "Lower Band", color=color.red)

REMA        = ta.wma(ta.wma(cprice, length), smooth)

c_up        = color.new(#33ff00, 0)
c_dn        = color.new(#ff1111, 0)

REMA_up     = REMA > REMA[1]

// Menampilkan SALMA hanya jika show_salma diaktifkan
plot(show_salma ? REMA : na, title='SALMA', color=REMA_up ? c_up : c_dn, linewidth=3)


// ======================================================================================================
// add optional MA's - to enable us to track what many other traders are working with
// These MA's will be hidden by default until user exposes them as needed in the Settings
// the below code is based on the built-in MA Ribbon in the TV library - with some modifications

// ======================================================================
f_ma(source, length, mtype) =>
    mtype    == 'SMA' ? ta.sma(source, length) :
      mtype  == 'EMA' ? ta.ema(source, length) :
      ta.wma(source, length)
// ======================================================================
gr_ma       = 'Optional MA\'s'
t_ma1       = 'MA #1'
t_ma2       = 'MA #2'
t_ma3       = 'MA #3'

// Inputs for MA1
show_ma1    = input.bool(false, t_ma1, inline=t_ma1, group=gr_ma)
ma1_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma1, group=gr_ma)
ma1_source  = input.source(close, '', inline=t_ma1, group=gr_ma)
ma1_length  = input.int(20, '', minval=1, inline=t_ma1, group=gr_ma)
ma1_color   = #59ff00
ma1         = f_ma(ma1_source, ma1_length, ma1_type)

// Inputs for MA2
show_ma2    = input.bool(false, t_ma2, inline=t_ma2, group=gr_ma)
ma2_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma2, group=gr_ma)
ma2_source  = input.source(close, '', inline=t_ma2, group=gr_ma)
ma2_length  = input.int(50, '', minval=1, inline=t_ma2, group=gr_ma)
ma2_color   = #ff7b5a
ma2         = f_ma(ma2_source, ma2_length, ma2_type)

// Inputs for MA3
show_ma3    = input.bool(false, t_ma3, inline=t_ma3, group=gr_ma)
ma3_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma3, group=gr_ma)
ma3_source  = input.source(close, '', inline=t_ma3, group=gr_ma)
ma3_length  = input.int(100, '', minval=1, inline=t_ma3, group=gr_ma)
ma3_color   = #ffc100
ma3         = f_ma(ma3_source, ma3_length, ma3_type)

// Plot and fill logic for MA1
plot0_ma1 = plot(ma1_source, display=display.none, editable=false)
css_ma1 = ma1_source > ma1 ? #0cb51a : #ff1100
plot1_ma1 = plot(show_ma1 ? ma1 : na, title=t_ma1, color=css_ma1, linewidth=1, display=show_ma1 ? display.all : display.none)
fill_css_ma1 = ma1_source > ma1 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma1, plot1_ma1, color=fill_css_ma1, title='Fill MA1')

// Plot and fill logic for MA2
plot0_ma2 = plot(ma2_source, display=display.none, editable=false)
css_ma2 = ma2_source > ma2 ? #0cb51a : #ff1100
plot1_ma2 = plot(show_ma2 ? ma2 : na, title=t_ma2, color=css_ma2, linewidth=1, display=show_ma2 ? display.all : display.none)
fill_css_ma2 = ma2_source > ma2 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma2, plot1_ma2, color=fill_css_ma2, title='Fill MA2')

// Plot and fill logic for MA3
plot0_ma3 = plot(ma3_source, display=display.none, editable=false)
css_ma3 = ma3_source > ma3 ? #0cb51a : #ff1100
plot1_ma3 = plot(show_ma3 ? ma3 : na, title=t_ma3, color=css_ma3, linewidth=1, display=show_ma3 ? display.all : display.none)
fill_css_ma3 = ma3_source > ma3 ? color.new(#0cb51a, 95) : color.new(#ff1100, 95)
fill(plot0_ma3, plot1_ma3, color=fill_css_ma3, title='Fill MA3')



// ================================================================================================================
// v3: Adding alerts for swing up/down and any swing
// ================================================================================================================

SwingDn     = REMA_up[1] and not(REMA_up)
SwingUp     = REMA_up    and not(REMA_up[1])

alertcondition(SwingUp, ". SALMA Swing Up", "SALMA Swing Up Detected!")                   // explicit swing up
alertcondition(SwingDn, ".. SALMA Swing Down", "SALMA Swing Down Detected!")              // explicit swing down
alertcondition(SwingUp or SwingDn, "... SALMA Swing", "SALMA Swing Detected!")            // Detect any swing
