// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © RedKTrader

//@version=5

indicator('Smooth And Lazy Moving Average & Order Block Detector', shorttitle = 'SALMA & OB', overlay = true)

g_slm = 'SALMA'
g_ob = 'Order Block Detector'

// Corrects price points within specific StdDev band before calculting a smoothed WMA

price       = input(close, 'Source', group = g_slm)
length      = input.int(10, 'Length', minval=1, group = g_slm)
smooth      = input.int(3, 'Extra Smooth [1 = None]', minval=1, group = g_slm)

mult        = input.float(0.3, minval=0.05, maxval=3, step=0.05,
  title='Width',  inline = 'SD Channel', group='Volatility Filter (SD Channel)')

sd_len      = input.int(5, minval=1,
  title='Length', inline = 'SD Channel', group='Volatility Filter (SD Channel)')

baseline    = ta.wma(price, sd_len)
dev         = mult * ta.stdev(price, sd_len)
upper       = baseline + dev
lower       = baseline - dev

cprice      = price > upper ? upper : price < lower ? lower : price

// Uncomment these code lines to expose the base StdDev channel used as volatility filter
//plot (baseline, "Base MA")
//plot(upper, "Upper Band", color=color.green)
//plot(lower, "Lower Band", color=color.red)

REMA        = ta.wma(ta.wma(cprice, length), smooth)

c_up        = color.new(#33ff00, 0)
c_dn        = color.new(#ff1111, 0)

REMA_up     = REMA > REMA[1]

plot(REMA, title='SALMA', color=REMA_up ? c_up : c_dn, linewidth=3)

// ======================================================================================================
// add optional MA's - to enable us to track what many other traders are working with
// These MA's will be hidden by default until user exposes them as needed in the Settings
// the below code is based on the built-in MA Ribbon in the TV library - with some modifications

// ======================================================================
f_ma(source, length, mtype) =>
    mtype    == 'SMA' ? ta.sma(source, length) :
      mtype  == 'EMA' ? ta.ema(source, length) :
      ta.wma(source, length)
// ======================================================================
gr_ma       = 'Optional MA\'s'
t_ma1       = 'MA #1'
t_ma2       = 'MA #2'
t_ma3       = 'MA #3'

show_ma1    = input.bool(false, t_ma1, inline=t_ma1, group=gr_ma)
ma1_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma1, group=gr_ma)
ma1_source  = input.source(close, '', inline=t_ma1, group=gr_ma)
ma1_length  = input.int(50, '', minval=1, inline=t_ma1, group=gr_ma)
ma1_color   = #9c27b0
ma1         = f_ma(ma1_source, ma1_length, ma1_type)
plot(show_ma1 ? ma1 : na, color = color.new(ma1_color, 0), title = t_ma1, linewidth = 1,
  display = show_ma1 ? display.all : display.none)

show_ma2    = input.bool(false, t_ma2, inline=t_ma2, group=gr_ma)
ma2_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma2, group=gr_ma)
ma2_source  = input.source(close, '', inline=t_ma2, group=gr_ma)
ma2_length  = input.int(100, '', minval=1, inline=t_ma2, group=gr_ma)
ma2_color   = #1163f6
ma2         = f_ma(ma2_source, ma2_length, ma2_type)
plot(show_ma2 ? ma2 : na, color = color.new(ma2_color, 0), title = t_ma2, linewidth = 1,
  display = show_ma2 ? display.all : display.none)

show_ma3    = input.bool(false, t_ma3, inline=t_ma3, group=gr_ma)
ma3_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA', 'HMA'], inline=t_ma3, group=gr_ma)
ma3_source  = input.source(close, '', inline=t_ma3, group=gr_ma)
ma3_length  = input.int(200, '', minval=1, inline=t_ma3, group=gr_ma)
ma3_color   = #1163f6
ma3         = f_ma(ma3_source, ma3_length, ma3_type)
plot(show_ma3 ? ma3 : na, color = color.new(ma3_color, 0), title = t_ma3, linewidth = 1,
  display = show_ma3 ? display.all : display.none)


// ================================================================================================================
// v3: Adding alerts for swing up/down and any swing
// ================================================================================================================

SwingDn     = REMA_up[1] and not(REMA_up)
SwingUp     = REMA_up    and not(REMA_up[1])

alertcondition(SwingUp, ". SALMA Swing Up", "SALMA Swing Up Detected!")                   // explicit swing up
alertcondition(SwingDn, ".. SALMA Swing Down", "SALMA Swing Down Detected!")              // explicit swing down
alertcondition(SwingUp or SwingDn, "... SALMA Swing", "SALMA Swing Detected!")            // Detect any swing

// Order Block Detector
//------------------------------------------------------------------------------
//Settings
//-----------------------------------------------------------------------------{
lengthOB = input.int(5, 'Volume Pivot Length'
  , minval = 1, group = g_ob)

bull_ext_last = input.int(3, 'Bullish OB '
  , minval = 1
  , inline = 'bull', group = g_ob)

bg_bull_css = input.color(color.new(#169400, 80), ''
  , inline = 'bull', group = g_ob)

bull_css = input.color(#169400, ''
  , inline = 'bull', group = g_ob)

bull_avg_css = input.color(color.new(#9598a1, 37), ''
  , inline = 'bull', group = g_ob)

bear_ext_last = input.int(3, 'Bearish OB'
  , minval = 1
  , inline = 'bear', group = g_ob)

bg_bear_css = input.color(color.new(#ff1100, 80), ''
  , inline = 'bear', group = g_ob)

bear_css = input.color(#ff1100, ''
  , inline = 'bear', group = g_ob)

bear_avg_css = input.color(color.new(#9598a1, 37), ''
  , inline = 'bear', group = g_ob)

line_style = input.string('⎯⎯⎯', 'Average Line Style'
  , options = ['⎯⎯⎯', '----', '····'], group = g_ob)

line_width = input.int(1, 'Average Line Width'
  , minval = 1, group = g_ob)

mitigation = input.string('Wick', 'Mitigation Methods'
  , options = ['Wick', 'Close'], group = g_ob)

//-----------------------------------------------------------------------------}
//Functions
//-----------------------------------------------------------------------------{
//Line Style function
get_line_style(style) =>
    out = switch style
        '⎯⎯⎯'  => line.style_solid
        '----' => line.style_dashed
        '····' => line.style_dotted

//Function to get order block coordinates
get_coordinates(condition, top, btm, ob_val)=>
    var ob_top  = array.new_float(0)
    var ob_btm  = array.new_float(0)
    var ob_avg  = array.new_float(0)
    var ob_left = array.new_int(0)

    float ob = na

    //Append coordinates to arrays
    if condition
        avg = math.avg(top, btm)

        array.unshift(ob_top, top)
        array.unshift(ob_btm, btm)
        array.unshift(ob_avg, avg)
        array.unshift(ob_left, time[lengthOB])

        ob := ob_val

    [ob_top, ob_btm, ob_avg, ob_left, ob]

//Function to remove mitigated order blocks from coordinate arrays
remove_mitigated(ob_top, ob_btm, ob_left, ob_avg, target, bull)=>
    mitigated = false
    target_array = bull ? ob_btm : ob_top

    for element in target_array
        idx = array.indexof(target_array, element)

        if (bull ? target < element : target > element)
            mitigated := true

            array.remove(ob_top, idx)
            array.remove(ob_btm, idx)
            array.remove(ob_avg, idx)
            array.remove(ob_left, idx)

    mitigated

//Function to set order blocks
set_order_blocks(ob_top, ob_btm, ob_left, ob_avg, ext_last, bg_css, border_css, lvl_css)=>
    var ob_box = array.new_box(0)
    var ob_lvl = array.new_line(0)

    //Fill arrays with boxes/lines
    if barstate.isfirst
        for i = 0 to ext_last-1
            array.unshift(ob_box, box.new(na,na,na,na
              , xloc = xloc.bar_time
              , extend= extend.right
              , bgcolor = bg_css
              , border_color = color.new(border_css, 70)))

            array.unshift(ob_lvl, line.new(na,na,na,na
              , xloc = xloc.bar_time
              , extend = extend.right
              , color = lvl_css
              , style = get_line_style(line_style)
              , width = line_width))

    //Set order blocks
    if barstate.islast
        if array.size(ob_top) > 0
            for i = 0 to math.min(ext_last-1, array.size(ob_top)-1)
                get_box = array.get(ob_box, i)
                get_lvl = array.get(ob_lvl, i)

                box.set_lefttop(get_box, array.get(ob_left, i), array.get(ob_top, i))
                box.set_rightbottom(get_box, array.get(ob_left, i), array.get(ob_btm, i))

                line.set_xy1(get_lvl, array.get(ob_left, i), array.get(ob_avg, i))
                line.set_xy2(get_lvl, array.get(ob_left, i)+1, array.get(ob_avg, i))


//-----------------------------------------------------------------------------}
//Global elements
//-----------------------------------------------------------------------------{
var os = 0
var target_bull = 0.
var target_bear = 0.

n = bar_index
upperOB = ta.highest(lengthOB)
lowerOB = ta.lowest(lengthOB)

if mitigation == 'Close'
    target_bull := ta.lowest(close, lengthOB)
    target_bear := ta.highest(close, lengthOB)
else
    target_bull := lowerOB
    target_bear := upperOB

os := high[lengthOB] > upperOB ? 0 : low[lengthOB] < lowerOB ? 1 : os[1]

phv = ta.pivothigh(volume, lengthOB, lengthOB)

//-----------------------------------------------------------------------------}
//Get bullish/bearish order blocks coordinates
//-----------------------------------------------------------------------------{
[bull_top
  , bull_btm
  , bull_avg
  , bull_left
  , bull_ob] = get_coordinates(phv and os == 1, hl2[lengthOB], low[lengthOB], low[lengthOB])

[bear_top
  , bear_btm
  , bear_avg
  , bear_left
  , bear_ob] = get_coordinates(phv and os == 0, high[lengthOB], hl2[lengthOB], high[lengthOB])

//-----------------------------------------------------------------------------}
//Remove mitigated order blocks
//-----------------------------------------------------------------------------{
mitigated_bull = remove_mitigated(bull_top
  , bull_btm
  , bull_left
  , bull_avg
  , target_bull
  , true)

mitigated_bear = remove_mitigated(bear_top
  , bear_btm
  , bear_left
  , bear_avg
  , target_bear
  , false)

//-----------------------------------------------------------------------------}
//Display order blocks
//-----------------------------------------------------------------------------{
//Set bullish order blocks
set_order_blocks(bull_top
  , bull_btm
  , bull_left
  , bull_avg
  , bull_ext_last
  , bg_bull_css
  , bull_css
  , bull_avg_css)

//Set bearish order blocks
set_order_blocks(bear_top
  , bear_btm
  , bear_left
  , bear_avg
  , bear_ext_last
  , bg_bear_css
  , bear_css
  , bear_avg_css)

//Show detected order blocks
plot(bull_ob, 'Bull OB', bull_css, 2, plot.style_linebr
  , offset = -lengthOB
  , display = display.none)

plot(bear_ob, 'Bear OB', bear_css, 2, plot.style_linebr
  , offset = -lengthOB
  , display = display.none)

//-----------------------------------------------------------------------------}
//Alerts
//-----------------------------------------------------------------------------{
alertcondition(bull_ob, 'Bullish OB Formed', 'Bullish order block detected')

alertcondition(bear_ob, 'Bearish OB Formed', 'bearish order block detected')

alertcondition(mitigated_bull, 'Bullish OB Mitigated', 'Bullish order block mitigated')

alertcondition(mitigated_bear, 'Bearish OB Mitigated', 'bearish order block mitigated')

//-----------------------------------------------------------------------------}