//@version=5

indicator('SuperTrended + Ultimate Moving Averages', 'ST + MA', overlay=true, format=format.price, precision=2, timeframe='', timeframe_gaps=false)

g_mamt = 'Moving Average Multi TimeFrame'
g_st = 'Supertrended'

srcST = input(close, title='Source', group = g_st)
mav = input.string(title='Moving Average Type', defval='EMA', options=['SMA', 'EMA', 'WMA', 'DEMA', 'TMA', 'VAR', 'WWMA', 'ZLEMA', 'TSF', 'HULL', 'TILL'], group = g_st)
length = input.int(100, 'Moving Average Length', minval=1, group = g_st)
Periods = input(title='ATR Period', defval=1, group = g_st)
Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=0.5, group = g_st)
changeATR = input(title='Change ATR Calculation Method ?', defval=true, group = g_st)
showsignals = input(title='Show Buy/Sell Signals ?', defval=false, group = g_st)
highlighting = input(title='Highlighter On/Off ?', defval=true, group = g_st)



T3a1 = input.float(0.7, 'TILLSON T3 Volume Factor', step=0.1)


Var_Func(srcST, length) =>
    valpha = 2 / (length + 1)
    vud1 = srcST > srcST[1] ? srcST - srcST[1] : 0
    vdd1 = srcST < srcST[1] ? srcST[1] - srcST : 0
    vUD = math.sum(vud1, 9)
    vDD = math.sum(vdd1, 9)
    vCMO = nz((vUD - vDD) / (vUD + vDD))
    VAR = 0.0
    VAR := nz(valpha * math.abs(vCMO) * srcST) + (1 - valpha * math.abs(vCMO)) * nz(VAR[1])
    VAR
VAR = Var_Func(srcST, length)
DEMA = 2 * ta.ema(srcST, length) - ta.ema(ta.ema(srcST, length), length)
Wwma_Func(srcST, length) =>
    wwalpha = 1 / length
    WWMA = 0.0
    WWMA := wwalpha * srcST + (1 - wwalpha) * nz(WWMA[1])
    WWMA
WWMA = Wwma_Func(srcST, length)
Zlema_Func(srcST, length) =>
    zxLag = length / 2 == math.round(length / 2) ? length / 2 : (length - 1) / 2
    zxEMAData = srcST + srcST - srcST[zxLag]
    ZLEMA = ta.ema(zxEMAData, length)
    ZLEMA
ZLEMA = Zlema_Func(srcST, length)
Tsf_Func(srcST, length) =>
    lrc = ta.linreg(srcST, length, 0)
    lrc1 = ta.linreg(srcST, length, 1)
    lrs = lrc - lrc1
    TSF = ta.linreg(srcST, length, 0) + lrs
    TSF
TSF = Tsf_Func(srcST, length)
HMA = ta.wma(2 * ta.wma(srcST, length / 2) - ta.wma(srcST, length), math.round(math.sqrt(length)))
T3e1 = ta.ema(srcST, length)
T3e2 = ta.ema(T3e1, length)
T3e3 = ta.ema(T3e2, length)
T3e4 = ta.ema(T3e3, length)
T3e5 = ta.ema(T3e4, length)
T3e6 = ta.ema(T3e5, length)
T3c1 = -T3a1 * T3a1 * T3a1
T3c2 = 3 * T3a1 * T3a1 + 3 * T3a1 * T3a1 * T3a1
T3c3 = -6 * T3a1 * T3a1 - 3 * T3a1 - 3 * T3a1 * T3a1 * T3a1
T3c4 = 1 + 3 * T3a1 + T3a1 * T3a1 * T3a1 + 3 * T3a1 * T3a1
T3 = T3c1 * T3e6 + T3c2 * T3e5 + T3c3 * T3e4 + T3c4 * T3e3


getMA(srcST, length) =>
    ma = 0.0
    if mav == 'SMA'
        ma := ta.sma(srcST, length)
        ma

    if mav == 'EMA'
        ma := ta.ema(srcST, length)
        ma

    if mav == 'WMA'
        ma := ta.wma(srcST, length)
        ma

    if mav == 'DEMA'
        ma := DEMA
        ma

    if mav == 'TMA'
        ma := ta.sma(ta.sma(srcST, math.ceil(length / 2)), math.floor(length / 2) + 1)
        ma

    if mav == 'VAR'
        ma := VAR
        ma

    if mav == 'WWMA'
        ma := WWMA
        ma

    if mav == 'ZLEMA'
        ma := ZLEMA
        ma

    if mav == 'TSF'
        ma := TSF
        ma

    if mav == 'HULL'
        ma := HMA
        ma

    if mav == 'TILL'
        ma := T3
        ma
    ma

MA = getMA(srcST, length)



atr2 = ta.sma(ta.tr, Periods)
atr = changeATR ? ta.atr(Periods) : atr2
up = MA - Multiplier * atr
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = MA + Multiplier * atr
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
upPlot = plot(trend == 1 ? up : na, title='Up Trend', color=color.new(color.green, 100), linewidth=0, style=plot.style_linebr)
buySignal = trend == 1 and trend[1] == -1
plotshape(buySignal ? up : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.green, 100))
plotshape(buySignal and showsignals ? up : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=color.new(color.green, 0), textcolor=color.new(color.white, 0))
dnPlot = plot(trend == 1 ? na : dn, title='Down Trend', style=plot.style_linebr, linewidth=0, color=color.new(color.red, 100))
sellSignal = trend == -1 and trend[1] == 1
plotshape(sellSignal ? dn : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.red, 100))
plotshape(sellSignal and showsignals ? dn : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=color.new(color.red, 0), textcolor=color.new(color.white, 0))
mPlot = plot(ohlc4, title='', style=plot.style_circles, linewidth=0)
colorup = input.color(defval = color.new(color.green, 60), title = "ColorU", inline = 'color')
colordown = input.color(defval = color.new(color.red, 60), title = "ColorD", inline = 'color')
longFillColor = highlighting ? trend == 1 ? colorup : color.white : color.new(color.white, 100)
shortFillColor = highlighting ? trend == -1 ? colordown : color.white : color.new(color.white, 100)
fill(mPlot, upPlot, title='UpTrend Highligter', color=longFillColor)
fill(mPlot, dnPlot, title='DownTrend Highligter', color=shortFillColor)
alertcondition(buySignal, title='SuperTrend Buy', message='SuperTrend Buy!')
alertcondition(sellSignal, title='SuperTrend Sell', message='SuperTrend Sell!')
changeCond = trend != trend[1]
alertcondition(changeCond, title='SuperTrend Direction Change', message='SuperTrend has changed direction!')


// Ultimate Moving Average-Multi-TimeFrame-7 MA Types
//inputs
src = close
useCurrentRes = input(true, title='Use Current Chart Resolution?', group = g_mamt)
resCustom = input.timeframe(title='Use Different Timeframe? Uncheck Box Above', defval='D', group = g_mamt)
len = input(100, title='Moving Average Length - LookBack Period', group = g_mamt)
atype = input.int(2, minval=1, maxval=7, title='1=SMA, 2=EMA, 3=WMA, 4=HullMA, 5=VWMA, 6=RMA, 7=TEMA', group = g_mamt)
cc = input(true, title='Change Color Based On Direction?', group = g_mamt)
smoothe = input.int(2, minval=1, maxval=10, title='Color Smoothing - 1 = No Smoothing', group = g_mamt)
doma2 = input(false, title='Optional 2nd Moving Average', group = g_mamt)
len2 = input(200, title='Moving Average Length - Optional 2nd MA', group = g_mamt)
atype2 = input.int(2, minval=1, maxval=7, title='1=SMA, 2=EMA, 3=WMA, 4=HullMA, 5=VWMA, 6=RMA, 7=TEMA', group = g_mamt)
cc2 = input(false, title='Change Color Based On Direction 2nd MA?', group = g_mamt)
warn = input(false, title='***You Can Turn On The Show Dots Parameter Below Without Plotting 2nd MA to See Crosses***', group = g_mamt)
warn2 = input(false, title='***If Using Cross Feature W/O Plotting 2ndMA - Make Sure 2ndMA Parameters are Set Correctly***', group = g_mamt)
sd = input(false, title='Show Dots on Cross of Both MA\'s', group = g_mamt)


res = useCurrentRes ? timeframe.period : resCustom
//hull ma definition
hullma = ta.wma(2 * ta.wma(src, len / 2) - ta.wma(src, len), math.round(math.sqrt(len)))
//TEMA definition
ema1 = ta.ema(src, len)
ema2 = ta.ema(ema1, len)
ema3 = ta.ema(ema2, len)
tema = 3 * (ema1 - ema2) + ema3

sma_1 = ta.sma(src, len)
ema_1 = ta.ema(src, len)
wma_1 = ta.wma(src, len)
vwma_1 = ta.vwma(src, len)
rma_1 = ta.rma(src, len)
avg = atype == 1 ? sma_1 : atype == 2 ? ema_1 : atype == 3 ? wma_1 : atype == 4 ? hullma : atype == 5 ? vwma_1 : atype == 6 ? rma_1 : tema
//2nd Ma - hull ma definition
hullma2 = ta.wma(2 * ta.wma(src, len2 / 2) - ta.wma(src, len2), math.round(math.sqrt(len2)))
//2nd MA TEMA definition
sema1 = ta.ema(src, len2)
sema2 = ta.ema(sema1, len2)
sema3 = ta.ema(sema2, len2)
stema = 3 * (sema1 - sema2) + sema3

sma_2 = ta.sma(src, len2)
ema_2 = ta.ema(src, len2)
wma_2 = ta.wma(src, len2)
vwma_2 = ta.vwma(src, len2)
rma_2 = ta.rma(src, len2)
avg2 = atype2 == 1 ? sma_2 : atype2 == 2 ? ema_2 : atype2 == 3 ? wma_2 : atype2 == 4 ? hullma2 : atype2 == 5 ? vwma_2 : atype2 == 6 ? rma_2 : tema

out = avg
out_two = avg2

out1 = request.security(syminfo.tickerid, res, out)
out2 = request.security(syminfo.tickerid, res, out_two)

ma_up = out1 >= out1[smoothe]
ma_down = out1 < out1[smoothe]

col = cc ? ma_up ? color.lime : ma_down ? color.red : color.aqua : color.aqua
col2 = cc2 ? ma_up ? color.lime : ma_down ? color.red : color.orange : color.orange

circleYPosition = out2

plot(out1, title='Multi-Timeframe Moving Avg', style=plot.style_line, linewidth=4, color=col)
plot(doma2 and out2 ? out2 : na, title='2nd Multi-TimeFrame Moving Average', style=plot.style_line, linewidth=4, color=col2)
plot(sd and ta.cross(out1, out2) ? circleYPosition : na, style=plot.style_cross, linewidth=5, color=color.new(color.yellow, 0))
