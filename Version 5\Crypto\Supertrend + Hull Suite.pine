//@version=5
indicator('Supertrend + Hull Suite', shorttitle='ST + HS', overlay=true, format=format.price, precision=2, timeframe='', max_labels_count = 500, max_lines_count = 500, max_boxes_count = 500, max_bars_back = 500)

PeriodsSR = input(title='ATR Period', defval=10)
src = input(hl2, title='Source')
Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0)
changeATR = input(title='Change ATR Calculation Method ?', defval=true)
showsignals = input(title='Show Buy/Sell Signals ?', defval=true)
highlighting = input(title='Highlighter On/Off ?', defval=true)
atr2 = ta.sma(ta.tr, PeriodsSR)
atr = changeATR ? ta.atr(PeriodsSR) : atr2
up = src - Multiplier * atr
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up
dn = src + Multiplier * atr
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn
trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
upPlot = plot(trend == 1 ? up : na, title='Up Trend', style=plot.style_linebr, linewidth=2, color=color.new(color.green, 0))
buySignal = trend == 1 and trend[1] == -1
plotshape(buySignal ? up : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.green, 0))
plotshape(buySignal and showsignals ? up : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=color.new(color.green, 0), textcolor=color.new(color.white, 0))
dnPlot = plot(trend == 1 ? na : dn, title='Down Trend', style=plot.style_linebr, linewidth=2, color=color.new(color.red, 0))
sellSignal = trend == -1 and trend[1] == 1
plotshape(sellSignal ? dn : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(color.red, 0))
plotshape(sellSignal and showsignals ? dn : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=color.new(color.red, 0), textcolor=color.new(color.white, 0))
mPlot = plot(ohlc4, title='', style=plot.style_circles, linewidth=0)
longFillColor = highlighting ? trend == 1 ? color.green : color.white : color.white
shortFillColor = highlighting ? trend == -1 ? color.red : color.white : color.white
fill(mPlot, upPlot, title='UpTrend Highligter', color=longFillColor, transp=90)
fill(mPlot, dnPlot, title='DownTrend Highligter', color=shortFillColor, transp=90)
alertcondition(buySignal, title='SuperTrend Buy', message='SuperTrend Buy!')
alertcondition(sellSignal, title='SuperTrend Sell', message='SuperTrend Sell!')
changeCond = trend != trend[1]
alertcondition(changeCond, title='SuperTrend Direction Change', message='SuperTrend has changed direction!')

// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © KivancOzbilgic

//Trend Magic
// period = input(20, 'CCI period')
// coeff = input(1, 'ATR Multiplier')
// AP = input(5, 'ATR Period')
// ATR = ta.sma(ta.tr, AP)
// srcTM = input(close)
// upT = low - ATR * coeff
// downT = high + ATR * coeff
// MagicTrend = 0.0
// MagicTrend := ta.cci(srcTM, period) >= 0 ? upT < nz(MagicTrend[1]) ? nz(MagicTrend[1]) : upT : downT > nz(MagicTrend[1]) ? nz(MagicTrend[1]) : downT
// color1 = ta.cci(srcTM, period) >= 0 ? #0022FC : #FC0400
// plot(MagicTrend, color=color1, linewidth=3)
// alertcondition(ta.cross(close, MagicTrend), title='Cross Alert', message='Price - MagicTrend Crossing!')
// alertcondition(ta.crossover(low, MagicTrend), title='CrossOver Alarm', message='BUY SIGNAL!')
// alertcondition(ta.crossunder(high, MagicTrend), title='CrossUnder Alarm', message='SELL SIGNAL!')

// Hull Suite
//INPUT
src_hs = input(close, title='Source')
modeSwitch = input.string('Hma', title='Hull Variation', options=['Hma', 'Thma', 'Ehma'])
length = input(55, title='Length(180-200 for floating S/R , 55 for swing entry)')
lengthMult = input(1.0, title='Length multiplier (Used to view higher timeframes with straight band)')

useHtf = input(false, title='Show Hull MA from X timeframe? (good for scalping)')
htf = input.timeframe('240', title='Higher timeframe')

switchColor = input(true, 'Color Hull according to trend?')
candleCol = input(false, title='Color candles based on Hull\'s Trend?')
visualSwitch = input(true, title='Show as a Band?')
thicknesSwitch = input(1, title='Line Thickness')
transpSwitch = input.int(40, title='Band Transparency', step=5)

//FUNCTIONS
//HMA
HMA(_src_hs, _length) =>
    ta.wma(2 * ta.wma(_src_hs, _length / 2) - ta.wma(_src_hs, _length), math.round(math.sqrt(_length)))
//EHMA
EHMA(_src_hs, _length) =>
    ta.ema(2 * ta.ema(_src_hs, _length / 2) - ta.ema(_src_hs, _length), math.round(math.sqrt(_length)))
//THMA
THMA(_src_hs, _length) =>
    ta.wma(ta.wma(_src_hs, _length / 3) * 3 - ta.wma(_src_hs, _length / 2) - ta.wma(_src_hs, _length), _length)

//SWITCH
Mode(modeSwitch, src_hs, len) =>
    modeSwitch == 'Hma' ? HMA(src_hs, len) : modeSwitch == 'Ehma' ? EHMA(src_hs, len) : modeSwitch == 'Thma' ? THMA(src_hs, len / 2) : na

//OUT
_hull = Mode(modeSwitch, src_hs, int(length * lengthMult))
HULL = useHtf ? request.security(syminfo.ticker, htf, _hull) : _hull
MHULL = HULL[0]
SHULL = HULL[2]

//COLOR
hullColor = switchColor ? HULL > HULL[2] ? #00ff00 : #ff0000 : #ff9800

//PLOT
///< Frame
Fi1 = plot(MHULL, title='MHULL', color=hullColor, linewidth=thicknesSwitch, transp=50)
Fi2 = plot(visualSwitch ? SHULL : na, title='SHULL', color=hullColor, linewidth=thicknesSwitch, transp=50)
alertcondition(ta.crossover(MHULL, SHULL), title='Hull trending up.', message='Hull trending up.')
alertcondition(ta.crossover(SHULL, MHULL), title='Hull trending down.', message='Hull trending down.')
///< Ending Filler
fill(Fi1, Fi2, title='Band Filler', color=hullColor, transp=transpSwitch)
///BARCOLOR
barcolor(color=candleCol ? switchColor ? hullColor : na : na)

// Support and Resistance
// left = input(10)
// right = input(10)

// hih = ta.pivothigh(high, left, right)
// lol = ta.pivotlow(low, left, right)

// top = ta.valuewhen(hih, high[right], 0)
// bot = ta.valuewhen(lol, low[right], 0)

// res = plot(top, color=top != top[1] ? na : #00ced1, offset=-left)
// sup = plot(bot, color=bot != bot[1] ? na : #dc143c, offset=-left)
